import Foundation
import MediaPipeTasksVision
import Combine
import CoreMotion
import UIKit

/// 仰卧起坐计数器
/// 基于 MediaPipe worldLandmarks 3D 坐标数据实现仰卧起坐动作检测和计数
class SitUpCounter: ObservableObject {

    // MARK: - 发布属性

    /// 当前仰卧起坐计数
    @Published var count: Int = 0

    /// 当前动作状态
    @Published var currentState: SitUpState = .ready

    /// 当前躯干角度（度）
    @Published var currentAngle: Double = 0.0

    /// 状态描述文本
    @Published var statusText: String = ""

    /// 提示信息
    @Published var hintText: String = ""

    // MARK: - 动作状态枚举

    /// 仰卧起坐动作状态
    enum SitUpState: String, CaseIterable {
        case ready = "ready"           // 准备状态
        case lyingDown = "lying_down"  // 躺下状态
        case sittingUp = "sitting_up"  // 起身状态
        case completed = "completed"   // 完成状态

        /// 获取本地化状态文本
        var localizedText: String {
            switch self {
            case .ready:
                return NSLocalizedString("situp_counter_ready", comment: "准备开始")
            case .lyingDown:
                return NSLocalizedString("situp_counter_lying_down", comment: "躺下")
            case .sittingUp:
                return NSLocalizedString("situp_counter_sitting_up", comment: "起身")
            case .completed:
                return NSLocalizedString("situp_counter_completed", comment: "完成")
            }
        }
    }

    // MARK: - 配置参数

    /// 检测配置参数结构体
    struct DetectionConfig {
        /// 躺下状态的最大角度阈值（度）- 躯干相对于水平面的倾斜角度
        /// 当躺下时，躯干接近水平，倾斜角度接近0度
        /// 设置为30度，允许一定的倾斜容差
        let lyingDownMaxAngle: Double = 30.0

        /// 起身状态的最小角度阈值（度）- 躯干相对于水平面的倾斜角度
        /// 当坐起时，躯干接近垂直，倾斜角度接近90度
        /// 设置为60度，确保躯干有足够的抬起幅度
        let sittingUpMinAngle: Double = 60.0

        /// 角度变化的最小阈值，用于防止误判（度）
        let angleChangeThreshold: Double = 10.0

        /// 状态保持的最小帧数，用于稳定检测
        let stateStabilityFrames: Int = 3

        /// 动作完成的冷却时间（秒）
        let actionCooldownTime: TimeInterval = 0.5
    }

    // MARK: - 私有属性

    /// 检测配置
    private let config = DetectionConfig()

    /// 状态稳定性计数器
    private var stateFrameCounter: Int = 0

    /// 上一次动作完成的时间戳
    private var lastActionTime: Date = Date()

    /// 角度历史记录（用于平滑处理）
    private var angleHistory: [Double] = []
    private let angleHistoryMaxSize: Int = 5

    /// 设备运动管理器，用于获取精确的重力向量
    private let deviceMotionManager = DeviceMotionManager()

    /// MediaPipe 人体关键点索引常量
    private struct LandmarkIndex {
        static let leftShoulder = 11   // 左肩
        static let rightShoulder = 12  // 右肩
        static let leftHip = 23        // 左髋
        static let rightHip = 24       // 右髋
        static let leftKnee = 25       // 左膝
        static let rightKnee = 26      // 右膝
    }

    // MARK: - 初始化

    init() {
        updateStatusText()
        updateHintText()

        // 启动设备运动数据更新
        deviceMotionManager.startMotionUpdates()
        DebugLogger.info(NSLocalizedString("device_motion_manager_started", comment: "设备运动管理器已启动"))
    }

    deinit {
        // 停止设备运动数据更新
        deviceMotionManager.stopMotionUpdates()
        DebugLogger.info(NSLocalizedString("device_motion_manager_stopped", comment: "设备运动管理器已停止"))
    }

    // MARK: - 公共方法

    /// 处理 MediaPipe 检测到的人体关键点数据
    /// - Parameters:
    ///   - worldLandmarks: MediaPipe 世界坐标系关键点数组
    ///   - deviceOrientation: 设备方向（可选）
    func processLandmarks(_ worldLandmarks: [Landmark], deviceOrientation: UIDeviceOrientation? = nil) {
        // 验证关键点数据的完整性
        guard worldLandmarks.count >= 33 else {
            handleError(.insufficientLandmarks)
            return
        }

        // 计算躯干角度
        guard let angle = calculateTorsoAngle(from: worldLandmarks, deviceOrientation: deviceOrientation) else {
            handleError(.invalidPose)
            return
        }

        // 更新角度历史记录并获取平滑后的角度
        let smoothedAngle = updateAngleHistory(angle)

        // 在主线程更新UI
        DispatchQueue.main.async { [weak self] in
            self?.currentAngle = smoothedAngle
            self?.processStateTransition(with: smoothedAngle)
        }
    }

    /// 重置计数器
    func reset() {
        DispatchQueue.main.async { [weak self] in
            self?.count = 0
            self?.currentState = .ready
            self?.currentAngle = 0.0
            self?.stateFrameCounter = 0
            self?.angleHistory.removeAll()
            self?.updateStatusText()
            self?.updateHintText()
        }
    }

    /// 获取设备运动状态信息（用于调试和监控）
    /// - Returns: 包含设备姿态和运动管理器状态的信息
    func getDeviceMotionInfo() -> (isAvailable: Bool, isActive: Bool, angles: (pitch: Double, roll: Double, yaw: Double)?) {
        return (
            isAvailable: deviceMotionManager.isAvailable,
            isActive: deviceMotionManager.isActive,
            angles: deviceMotionManager.getDeviceAngles()
        )
    }

    // MARK: - 私有方法

    /// 计算躯干倾斜角度（相对于水平面）
    /// - Parameters:
    ///   - worldLandmarks: 世界坐标系关键点数组
    ///   - deviceOrientation: 设备方向
    /// - Returns: 躯干相对于水平面的倾斜角度（度），如果计算失败返回 nil
    ///
    /// 角度定义：
    /// - 0°：躯干完全水平（躺下）
    /// - 90°：躯干完全垂直（站立）
    /// - 45°：躯干与水平面成45度角
    private func calculateTorsoAngle(from worldLandmarks: [Landmark], deviceOrientation: UIDeviceOrientation? = nil) -> Double? {
        // 获取关键点
        let leftShoulder = worldLandmarks[LandmarkIndex.leftShoulder]
        let rightShoulder = worldLandmarks[LandmarkIndex.rightShoulder]
        let leftHip = worldLandmarks[LandmarkIndex.leftHip]
        let rightHip = worldLandmarks[LandmarkIndex.rightHip]

        // 计算肩部和髋部的中点（使用3D坐标）
        let shoulderMidpoint = (
            x: (leftShoulder.x + rightShoulder.x) / 2,
            y: (leftShoulder.y + rightShoulder.y) / 2,
            z: (leftShoulder.z + rightShoulder.z) / 2
        )

        let hipMidpoint = (
            x: (leftHip.x + rightHip.x) / 2,
            y: (leftHip.y + rightHip.y) / 2,
            z: (leftHip.z + rightHip.z) / 2
        )

        // 计算躯干3D向量（从髋部指向肩部）
        let torsoVector = (
            x: shoulderMidpoint.x - hipMidpoint.x,
            y: shoulderMidpoint.y - hipMidpoint.y,
            z: shoulderMidpoint.z - hipMidpoint.z
        )

        // 获取重力向量（用于确定垂直方向）
        let gravityVector = getGravityVector(for: deviceOrientation)

        // 计算躯干向量的长度
        let torsoLength = sqrt(torsoVector.x * torsoVector.x +
                              torsoVector.y * torsoVector.y +
                              torsoVector.z * torsoVector.z)

        // 防止除零错误
        guard torsoLength > 0.001 else {
            DebugLogger.warning("躯干向量长度过小，无法计算角度")
            return nil
        }

        // 标准化躯干向量
        let normalizedTorso = (
            x: torsoVector.x / torsoLength,
            y: torsoVector.y / torsoLength,
            z: torsoVector.z / torsoLength
        )

        // 基于实际测试结果重新设计角度计算逻辑
        //
        // 实际测试发现：
        // 1. 当人站立时，计算出的角度只有 7.7°，但应该接近 90°
        // 2. 这说明我们的角度计算逻辑完全错误
        // 3. 需要重新理解 MediaPipe worldLandmarks 坐标系

        // 重新分析：如果站立时角度只有 7.7°，说明：
        // - 要么我们的角度定义错误
        // - 要么 MediaPipe 坐标系与我们理解的不同

        // 新的假设：MediaPipe worldLandmarks 可能使用不同的坐标系约定
        // 让我们尝试不同的角度计算方法

        // 方法1：直接使用 Y 分量作为垂直度指标
        let yComponent = normalizedTorso.y
        let yComponentAbs = abs(yComponent)

        // 方法2：计算躯干向量与垂直方向的夹角（使用标准重力向量）
        let standardGravity = (x: Float(0.0), y: Float(-1.0), z: Float(0.0))
        let dotProductWithStandardGravity = normalizedTorso.x * standardGravity.x +
                                           normalizedTorso.y * standardGravity.y +
                                           normalizedTorso.z * standardGravity.z
        let angleWithVertical = acos(max(-1.0, min(1.0, abs(Double(dotProductWithStandardGravity)))))
        let angleWithVerticalDegrees = angleWithVertical * 180.0 / .pi

        // 方法3：计算躯干向量与水平面的夹角（几何方法）
        let horizontalLength = sqrt(normalizedTorso.x * normalizedTorso.x + normalizedTorso.z * normalizedTorso.z)
        let angleWithHorizontal = atan2(Double(yComponentAbs), Double(horizontalLength))
        let angleWithHorizontalDegrees = angleWithHorizontal * 180.0 / .pi

        // 方法4：反向思考 - 如果当前结果是错误的，尝试补角
        let complementaryAngle = 90.0 - angleWithHorizontalDegrees

        // 获取重力向量信息用于诊断
        let gravityMagnitude = sqrt(gravityVector.x * gravityVector.x + gravityVector.y * gravityVector.y + gravityVector.z * gravityVector.z)

        // 暂时使用几何方法，但我们需要验证哪个是正确的
        let angleDegrees = angleWithHorizontalDegrees

        // 详细调试输出（帮助诊断角度计算问题）
        DebugLogger.info("=== 躯干角度计算诊断 ===")
        DebugLogger.info("肩部中点: (\(String(format: "%.3f", shoulderMidpoint.x)), \(String(format: "%.3f", shoulderMidpoint.y)), \(String(format: "%.3f", shoulderMidpoint.z)))")
        DebugLogger.info("髋部中点: (\(String(format: "%.3f", hipMidpoint.x)), \(String(format: "%.3f", hipMidpoint.y)), \(String(format: "%.3f", hipMidpoint.z)))")
        DebugLogger.info("躯干向量: (\(String(format: "%.3f", torsoVector.x)), \(String(format: "%.3f", torsoVector.y)), \(String(format: "%.3f", torsoVector.z)))")
        DebugLogger.info("标准化躯干向量: (\(String(format: "%.3f", normalizedTorso.x)), \(String(format: "%.3f", normalizedTorso.y)), \(String(format: "%.3f", normalizedTorso.z)))")
        DebugLogger.info("重力向量: (\(String(format: "%.3f", gravityVector.x)), \(String(format: "%.3f", gravityVector.y)), \(String(format: "%.3f", gravityVector.z)))")

        // 对比所有计算方法的结果
        DebugLogger.info("--- 所有角度计算方法对比 ---")
        DebugLogger.info("Y分量: \(String(format: "%.3f", yComponent)) (绝对值: \(String(format: "%.3f", yComponentAbs)))")
        DebugLogger.info("水平长度: \(String(format: "%.3f", horizontalLength))")
        DebugLogger.info("方法1 - Y分量绝对值: \(String(format: "%.1f", yComponentAbs * 180.0 / .pi))°")
        DebugLogger.info("方法2 - 与垂直方向夹角: \(String(format: "%.1f", angleWithVerticalDegrees))°")
        DebugLogger.info("方法3 - 与水平面夹角: \(String(format: "%.1f", angleWithHorizontalDegrees))°")
        DebugLogger.info("方法4 - 补角: \(String(format: "%.1f", complementaryAngle))°")
        DebugLogger.info("重力向量长度: \(String(format: "%.3f", gravityMagnitude))")

        DebugLogger.info("🎯 当前选择角度: \(String(format: "%.1f", angleDegrees))° (方法3-几何)")

        // 基于实际测试结果的分析
        if angleDegrees < 15 {
            DebugLogger.info("📊 分析：角度很小，可能是躺下状态或计算错误")
        } else if angleDegrees > 75 {
            DebugLogger.info("📊 分析：角度很大，可能是站立状态")
        } else {
            DebugLogger.info("📊 分析：中等角度，可能是过渡状态")
        }

        // 如果当前方法给出的结果不合理，尝试其他方法
        if angleDegrees < 15 && complementaryAngle > 75 {
            DebugLogger.warning("🔄 当前角度过小，可能需要使用补角: \(String(format: "%.1f", complementaryAngle))°")
        }

        // 验证角度合理性
        if angleDegrees < 0 || angleDegrees > 90 {
            DebugLogger.warning("计算出的倾斜角度超出合理范围: \(angleDegrees)°")
        }

        return angleDegrees
    }

    /// 更新角度历史记录并返回平滑后的角度
    /// - Parameter newAngle: 新的角度值
    /// - Returns: 平滑后的角度值
    private func updateAngleHistory(_ newAngle: Double) -> Double {
        angleHistory.append(newAngle)

        // 保持历史记录大小限制
        if angleHistory.count > angleHistoryMaxSize {
            angleHistory.removeFirst()
        }

        // 计算平均值进行平滑处理
        let smoothedAngle = angleHistory.reduce(0, +) / Double(angleHistory.count)
        DebugLogger.info("原始角度: \(newAngle)°, 平滑后角度: \(smoothedAngle)°")

        return smoothedAngle
    }

    /// 处理状态转换逻辑
    /// - Parameter angle: 当前躯干角度
    private func processStateTransition(with angle: Double) {
        let newState = determineState(from: angle)

        // 检查状态是否发生变化
        if newState == currentState {
            stateFrameCounter += 1
        } else {
            DebugLogger.info("状态变化: \(currentState.rawValue) -> \(newState.rawValue), 角度: \(angle)°")
            stateFrameCounter = 1
            currentState = newState
        }

        // 调试输出当前状态信息
        DebugLogger.info("当前状态: \(currentState.rawValue), 角度: \(angle)°, 稳定帧数: \(stateFrameCounter)/\(config.stateStabilityFrames)")

        // 只有在状态稳定后才处理动作逻辑
        if stateFrameCounter >= config.stateStabilityFrames {
            handleStateAction()
        }

        updateStatusText()
        updateHintText()
    }

    /// 根据倾斜角度确定当前状态
    /// - Parameter angle: 躯干相对于水平面的倾斜角度（度）
    /// - Returns: 对应的状态
    ///
    /// 状态判断逻辑：
    /// - 0°-30°：躯干接近水平，判定为躺下状态
    /// - 60°-90°：躯干接近垂直，判定为坐起状态
    /// - 30°-60°：中间过渡区域，保持当前状态避免频繁切换
    private func determineState(from angle: Double) -> SitUpState {
        switch angle {
        case 0...config.lyingDownMaxAngle:
            // 角度小，躯干接近水平，处于躺下状态
            return .lyingDown
        case config.sittingUpMinAngle...90.0:
            // 角度大，躯干接近垂直，处于坐起状态
            return .sittingUp
        default:
            // 中间角度（30°-60°），保持当前状态，避免频繁切换
            return currentState
        }
    }

    /// 处理状态相关的动作逻辑
    private func handleStateAction() {
        let currentTime = Date()

        // 检查冷却时间
        guard currentTime.timeIntervalSince(lastActionTime) >= config.actionCooldownTime else {
            return
        }

        // 检测完整的仰卧起坐动作：从躺下到起身
        if currentState == .sittingUp && stateFrameCounter == config.stateStabilityFrames {
            // 检查是否从躺下状态转换而来
            if angleHistory.count >= 2 {
                let previousAngles = Array(angleHistory.prefix(angleHistory.count - 1))
                // 修正：检查历史角度中是否有躺下状态（角度小于等于躺下最大角度）
                let hadLyingState = previousAngles.contains { $0 <= config.lyingDownMaxAngle }

                DebugLogger.info("检测仰卧起坐动作: 历史角度 \(previousAngles), 是否有躺下状态: \(hadLyingState)")

                if hadLyingState {
                    DebugLogger.info("检测到完整仰卧起坐动作！计数增加")
                    incrementCount()
                    lastActionTime = currentTime
                }
            }
        }
    }

    /// 增加计数
    private func incrementCount() {
        count += 1
        currentState = .completed

        // 短暂显示完成状态后重置为准备状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.currentState = .ready
            self?.updateStatusText()
            self?.updateHintText()
        }
    }

    /// 更新状态文本
    private func updateStatusText() {
        statusText = currentState.localizedText
    }

    /// 更新提示文本
    private func updateHintText() {
        switch currentState {
        case .ready:
            hintText = NSLocalizedString("situp_hint_position", comment: "请躺下，保持膝盖弯曲")
        case .lyingDown:
            hintText = NSLocalizedString("situp_hint_start", comment: "开始仰卧起坐动作")
        case .sittingUp:
            hintText = NSLocalizedString("situp_hint_continue", comment: "继续保持动作")
        case .completed:
            hintText = NSLocalizedString("situp_hint_good", comment: "动作标准！")
        }
    }

    /// 处理错误情况
    /// - Parameter error: 错误类型
    private func handleError(_ error: SitUpError) {
        DispatchQueue.main.async { [weak self] in
            switch error {
            case .noLandmarks:
                self?.hintText = NSLocalizedString("situp_error_no_landmarks", comment: "未检测到人体关键点")
            case .insufficientLandmarks:
                self?.hintText = NSLocalizedString("situp_error_insufficient_landmarks", comment: "关键点数据不足")
            case .invalidPose:
                self?.hintText = NSLocalizedString("situp_error_invalid_pose", comment: "姿势不正确")
            }
        }
    }

    /// 获取标准化的重力向量（在 MediaPipe worldLandmarks 坐标系中）
    /// - Parameter deviceOrientation: 设备方向（此参数保留用于兼容性，但不影响计算）
    /// - Returns: 重力方向的3D向量
    ///
    /// 重要说明：MediaPipe worldLandmarks 使用固定的世界坐标系，不受设备方向影响。
    /// 该坐标系以人体为中心：
    /// - X轴：人体左右方向（正值向人体右侧）
    /// - Y轴：人体上下方向（正值向人体上方）
    /// - Z轴：人体前后方向（正值向人体前方）
    ///
    /// 重力始终指向地面方向，在此坐标系中为 Y轴负方向 (0, -1, 0)
    private func getGravityVector(for deviceOrientation: UIDeviceOrientation?) -> (x: Float, y: Float, z: Float) {
        // 优先使用设备运动管理器的精确重力向量
        if let preciseGravity = deviceMotionManager.getPreciseGravityVector() {
            DebugLogger.debug(NSLocalizedString("device_motion_precise_gravity_available", comment: "使用精确重力向量") + ": (\(preciseGravity.x), \(preciseGravity.y), \(preciseGravity.z))")
            return preciseGravity
        }

        // 如果精确重力向量不可用，使用标准重力向量
        DebugLogger.warning(NSLocalizedString("device_motion_precise_gravity_unavailable", comment: "精确重力向量不可用，使用标准重力向量"))

        // MediaPipe worldLandmarks 坐标系中的标准重力向量
        // 重力始终指向地面（Y轴负方向），不受设备方向影响
        // 这确保了相同的身体姿势在不同设备方向下计算出一致的角度
        let standardGravity = (x: 0.0, y: -1.0, z: 0.0)

        DebugLogger.info("使用标准重力向量: (\(standardGravity.x), \(standardGravity.y), \(standardGravity.z))")

        return (x: Float(standardGravity.x), y: Float(standardGravity.y), z: Float(standardGravity.z))
    }
}

// MARK: - 错误类型定义

/// 仰卧起坐检测错误类型
enum SitUpError: Error {
    case noLandmarks           // 没有检测到关键点
    case insufficientLandmarks // 关键点数据不足
    case invalidPose          // 姿势无效
}
