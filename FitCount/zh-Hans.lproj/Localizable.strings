/* FitCount Chinese (Simplified) Localizations */

"tab.first.title" = "模块一";
"tab.middle.title" = "核心功能";
"tab.last.title" = "模块二";

"view.first.content" = "这是第一个模块的内容区域。";
"view.middle.content" = "这是核心功能模块的内容区域。";
"view.last.content" = "这是第二个模块的内容区域。";

"view.first.button.goToDetail" = "跳转到详情页";

// 详情页本地化
"view.first.detail.title" = "详情页面";
"view.first.detail.content" = "这是从第一个模块跳转过来的全屏详情页内容。";
"view.first.detail.navigationTitle" = "模块一详情";
"button.done" = "完成";

// FirstView本地化
"exercise" = "运动";
"exercise.selection.description" = "选择你想进行的运动,所有的数据仅存在您的手机上";
"exercise.situp" = "仰卧起坐";
"exercise.pullup" = "引体向上";
"exercise.pushup" = "俯卧撑";
"exercise.squat" = "深蹲";
"exercise.plank" = "平板支撑";
"error.prefix" = "错误: ";

// 相机权限提示
"camera.permission.title" = "需要相机权限";
"camera.permission.message" = "请前往设置开启相机权限以使用此功能";
"camera.permission.ok" = "确定";

// 设备方向和坐标转换相关
"orientation.portrait" = "竖屏";
"orientation.landscape.left" = "横屏左";
"orientation.landscape.right" = "横屏右";
"orientation.unknown" = "未知方向";
"pose.overlay.coordinate.transform" = "姿态坐标转换";
"pose.overlay.orientation.changed" = "设备方向已改变";

// MARK: - 仰卧起坐计数器相关文本

/* 仰卧起坐计数器状态 */
"situp_counter_ready" = "准备开始";
"situp_counter_lying_down" = "躺下";
"situp_counter_sitting_up" = "起身";
"situp_counter_completed" = "完成";

/* 仰卧起坐计数显示 */
"situp_count_label" = "仰卧起坐: %d";
"situp_angle_label" = "角度: %.1f°";
"situp_status_label" = "状态: %@";

/* 仰卧起坐提示信息 */
"situp_hint_position" = "请躺下，保持膝盖弯曲";
"situp_hint_start" = "开始仰卧起坐动作";
"situp_hint_continue" = "继续保持动作";
"situp_hint_good" = "动作标准！";

/* 错误信息 */
"situp_error_no_landmarks" = "未检测到人体关键点";
"situp_error_insufficient_landmarks" = "关键点数据不足";
"situp_error_invalid_pose" = "姿势不正确";

// MARK: - 设备运动传感器相关文本

/* 设备运动状态 */
"device_motion_sensor_available" = "传感器可用";
"device_motion_sensor_unavailable" = "传感器不可用";
"device_motion_sensor_active" = "运行中";
"device_motion_sensor_inactive" = "已停止";

/* 设备姿态角度 */
"device_motion_pitch" = "俯仰角";
"device_motion_roll" = "翻滚角";
"device_motion_yaw" = "偏航角";

/* 设备倾斜状态 */
"device_tilt_level" = "水平";
"device_tilt_slight" = "轻微倾斜";
"device_tilt_moderate" = "中度倾斜";
"device_tilt_significant" = "大幅倾斜";
"device_tilt_severe" = "严重倾斜";

/* 设备倾斜方向 */
"device_tilt_direction_forward_backward" = "前后倾斜";
"device_tilt_direction_left_right" = "左右倾斜";
"device_tilt_degree_format" = "倾斜度: %.1f°";

/* 设备运动管理器日志 */
"device_motion_manager_started" = "设备运动管理器已启动";
"device_motion_manager_stopped" = "设备运动管理器已停止";
"device_motion_data_available" = "设备运动数据可用";
"device_motion_data_unavailable" = "设备运动数据不可用";
"device_motion_precise_gravity_available" = "使用精确重力向量";
"device_motion_precise_gravity_unavailable" = "精确重力向量不可用，使用标准重力向量";

/* 坐标系转换和角度计算相关 */
"coordinate_system_world_landmarks" = "MediaPipe 世界坐标系";
"coordinate_system_device_coordinates" = "设备坐标系";
"gravity_vector_standard" = "标准重力向量";
"gravity_vector_precise" = "精确重力向量";
"angle_calculation_consistent" = "角度计算一致性修正";
"device_orientation_independence" = "设备方向无关性";
"torso_angle_calculation" = "躯干角度计算";
"pose_detection_accuracy" = "姿态检测精度";

/* 设置页面 */
"settings_device_motion_section_title" = "设备运动状态";
"settings_device_motion_section_footer" = "设备运动传感器用于精确计算重力方向，提高角度检测准确性";
"settings_sensor_availability" = "传感器可用性";
"settings_sensor_status" = "传感器状态";
"settings_device_attitude_angles" = "设备姿态角度";
"settings_device_tilt_status" = "设备倾斜状态";