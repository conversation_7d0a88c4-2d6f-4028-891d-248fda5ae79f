默认	17:02:58.174182+0800	FitCount	Handshake succeeded
默认	17:02:58.174228+0800	FitCount	Identity resolved as app<com.jack.FitCount(D179D11A-42C7-4416-A55B-8D29D4BDD786)>
默认	17:02:58.174819+0800	FitCount	Deactivation reason added: 10; deactivation reasons: 0 -> 1024; animating application lifecycle event: 0
默认	17:02:58.174873+0800	FitCount	activating monitor for service com.apple.frontboard.open
默认	17:02:58.175774+0800	FitCount	Incrementing reference count for background assertion <private>
默认	17:02:58.175822+0800	FitCount	Created background task <private>.
默认	17:02:58.175920+0800	FitCount	activating monitor for service com.apple.frontboard.workspace-service
默认	17:02:58.175966+0800	FitCount	Realizing settings extension _UIApplicationSceneKeyboardSettings on FBSSceneSettings
默认	17:02:58.176013+0800	FitCount	FBSWorkspace registering source: <private>
默认	17:02:58.177429+0800	FitCount	FBSWorkspace connected to endpoint : <private>
默认	17:02:58.177576+0800	FitCount	Realizing settings extension <_UISceneOcclusionSettings> on FBSSceneSettings
默认	17:02:58.177620+0800	FitCount	<FBSWorkspaceScenesClient:0x12a1cdc80 <private>> attempting immediate handshake from activate
默认	17:02:58.177664+0800	FitCount	<FBSWorkspaceScenesClient:0x12a1cdc80 <private>> sent handshake
默认	17:02:58.178785+0800	FitCount	Added observer for process assertions expiration warning: <_RBSExpirationWarningClient: 0x12a16b460>
默认	17:02:58.179270+0800	FitCount	Realizing settings extension <_UISceneInterfaceProtectionSceneSettings> on FBSSceneSettings
默认	17:02:58.183182+0800	FitCount	UIMutableApplicationSceneSettings setting counterpart class: UIApplicationSceneSettings
默认	17:02:58.183264+0800	FitCount	UIMutableApplicationSceneClientSettings setting counterpart class: UIApplicationSceneClientSettings
默认	17:02:58.183440+0800	FitCount	Deactivation reason removed: 10; deactivation reasons: 3072 -> 2048; animating application lifecycle event: 0
默认	17:02:58.183751+0800	FitCount	Event Timing Profile for Touch: ok, path="/System/Library/EventTimingProfiles/D27.Touch.plist"
默认	17:02:58.183888+0800	FitCount	Event Timing Profile for Pencil: not found, path="/System/Library/EventTimingProfiles/D27.Pencil.plist"
默认	17:02:58.184171+0800	FitCount	Selected display: name=LCD (primary), id=1
默认	17:02:58.185691+0800	FitCount	Deactivation reason added: 5; deactivation reasons: 2048 -> 2080; animating application lifecycle event: 1
默认	17:02:58.186031+0800	FitCount	Should send trait collection or coordinate space update, interface style 1 -> 1, <UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70)
默认	17:02:58.186223+0800	FitCount	Not push traits update to screen for new style 1, <UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70)
默认	17:02:58.190035+0800	FitCount	Realizing settings extension <_UIApplicationSceneDisplaySettings> on FBSSceneSettings
默认	17:02:58.191281+0800	FitCount	<UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70) Scene updated orientation preferences: none -> ( Pu Ll Lr )
默认	17:02:58.191329+0800	FitCount	Key window API is scene-level: YES
默认	17:02:58.191389+0800	FitCount	UIWindowScene: 0x10283b750: Window became key in scene: UIWindow: 0x102839820; contextId: 0x45E1D5CA: reason: UIWindowScene: 0x10283b750: Window requested to become key in scene: 0x102839820
默认	17:02:58.191446+0800	FitCount	Key window needs update: 1; currentKeyWindowScene: 0x0; evaluatedKeyWindowScene: 0x10283b750; currentApplicationKeyWindow: 0x0; evaluatedApplicationKeyWindow: 0x102839820; reason: UIWindowScene: 0x10283b750: Window requested to become key in scene: 0x102839820
默认	17:02:58.191529+0800	FitCount	Window did become application key: UIWindow: 0x102839820; contextId: 0x45E1D5CA; scene identity: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:58.191628+0800	FitCount	[0x12a1ec690] Begin local event deferring requested for token: 0x12a18c240; environments: 1; reason: UIWindowScene: 0x10283b750: Begin event deferring in keyboardFocus for window: 0x102839820
默认	17:02:58.192102+0800	FitCount	Not push traits update to screen for new style 1, <UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70)
默认	17:02:58.193013+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:58.193067+0800	FitCount	Ignoring already applied deactivation reason: 5; deactivation reasons: 2080
默认	17:02:58.193165+0800	FitCount	Deactivation reason added: 12; deactivation reasons: 2080 -> 6176; animating application lifecycle event: 1
默认	17:02:58.193351+0800	FitCount	Deactivation reason removed: 11; deactivation reasons: 6176 -> 4128; animating application lifecycle event: 1
默认	17:02:58.193607+0800	FitCount	[0x12a1c88c0] Session created from connection [0x12a1d0400]
默认	17:02:58.193634+0800	FitCount	[0x12a1d0400] activating connection: mach=true listener=false peer=false name=com.apple.uiintelligencesupport.agent
默认	17:02:58.193659+0800	FitCount	[0x12a1c88c0] Session activated
默认	17:02:58.199932+0800	FitCount	Not observing PTDefaults on customer install.
默认	17:02:58.201344+0800	FitCount	[0x12a1d9cc0] activating connection: mach=true listener=false peer=false name=com.apple.fontservicesd
默认	17:02:58.174205+0800	FitCount	Handshake succeeded
默认	17:02:58.174252+0800	FitCount	Identity resolved as app<com.jack.FitCount(D179D11A-42C7-4416-A55B-8D29D4BDD786)>
默认	17:02:58.174845+0800	FitCount	Deactivation reason added: 10; deactivation reasons: 0 -> 1024; animating application lifecycle event: 0
默认	17:02:58.175002+0800	FitCount	activating monitor for service com.apple.frontboard.open
默认	17:02:58.175797+0800	FitCount	Incrementing reference count for background assertion <private>
默认	17:02:58.175846+0800	FitCount	Created background task <private>.
默认	17:02:58.175941+0800	FitCount	activating monitor for service com.apple.frontboard.workspace-service
默认	17:02:58.175988+0800	FitCount	Realizing settings extension _UIApplicationSceneKeyboardSettings on FBSSceneSettings
默认	17:02:58.176071+0800	FitCount	FBSWorkspace registering source: <private>
默认	17:02:58.177452+0800	FitCount	FBSWorkspace connected to endpoint : <private>
默认	17:02:58.177597+0800	FitCount	Realizing settings extension <_UISceneOcclusionSettings> on FBSSceneSettings
默认	17:02:58.177643+0800	FitCount	<FBSWorkspaceScenesClient:0x12a1cdc80 <private>> attempting immediate handshake from activate
默认	17:02:58.177686+0800	FitCount	<FBSWorkspaceScenesClient:0x12a1cdc80 <private>> sent handshake
默认	17:02:58.214555+0800	FitCount	Create activity from XPC object <nw_activity 50:1 [F97600F2-4BEB-4965-B8A2-E540AFDA985A] (reporting strategy default)>
默认	17:02:58.214608+0800	FitCount	Create activity from XPC object <nw_activity 50:2 [F16474D4-AD21-43DE-A206-F1D23C2E9DF3] (reporting strategy default)>
默认	17:02:58.214662+0800	FitCount	Set activity <nw_activity 50:1 [F97600F2-4BEB-4965-B8A2-E540AFDA985A] (reporting strategy default)> as the global parent
默认	17:02:58.214766+0800	FitCount	AggregateDictionary is deprecated and has been removed. Please migrate to Core Analytics.
默认	17:02:58.214864+0800	FitCount	Ending background task with UIBackgroundTaskIdentifier: 1
默认	17:02:58.214920+0800	FitCount	Ending task with identifier 1 and description: <private>, _expireHandler: (null)
默认	17:02:58.178811+0800	FitCount	Added observer for process assertions expiration warning: <_RBSExpirationWarningClient: 0x12a16b460>
默认	17:02:58.215032+0800	FitCount	Decrementing reference count for assertion <private> (used by background task with identifier 1: <private>)
默认	17:02:58.215178+0800	FitCount	Will invalidate assertion: <BKSProcessAssertion: 0x12a1c81e0> for task identifier: 1
默认	17:02:58.179294+0800	FitCount	Realizing settings extension <_UISceneInterfaceProtectionSceneSettings> on FBSSceneSettings
默认	17:02:58.215617+0800	FitCount	Target list changed: <CADisplay:LCD primary>
默认	17:02:58.219368+0800	FitCount	startConnection
默认	17:02:58.221143+0800	FitCount	[0x12ae303c0] activating connection: mach=true listener=false peer=false name=com.apple.UIKit.KeyboardManagement.hosted
默认	17:02:58.221275+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:58.222984+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:58.183206+0800	FitCount	UIMutableApplicationSceneSettings setting counterpart class: UIApplicationSceneSettings
默认	17:02:58.183290+0800	FitCount	UIMutableApplicationSceneClientSettings setting counterpart class: UIApplicationSceneClientSettings
默认	17:02:58.183465+0800	FitCount	Deactivation reason removed: 10; deactivation reasons: 3072 -> 2048; animating application lifecycle event: 0
默认	17:02:58.183774+0800	FitCount	Event Timing Profile for Touch: ok, path="/System/Library/EventTimingProfiles/D27.Touch.plist"
默认	17:02:58.183912+0800	FitCount	Event Timing Profile for Pencil: not found, path="/System/Library/EventTimingProfiles/D27.Pencil.plist"
默认	17:02:58.241977+0800	FitCount	handleKeyboardChange: set currentKeyboard:N (wasKeyboard:N)
默认	17:02:58.242186+0800	FitCount	forceReloadInputViews
默认	17:02:58.184195+0800	FitCount	Selected display: name=LCD (primary), id=1
默认	17:02:58.242234+0800	FitCount	Reloading input views for: <(null): 0x0; > force: 1
默认	17:02:58.242403+0800	FitCount	isWritingToolsHandlingKeyboardTracking:Y (WT ready:Y, Arbiter ready:Y)
默认	17:02:58.185715+0800	FitCount	Deactivation reason added: 5; deactivation reasons: 2048 -> 2080; animating application lifecycle event: 1
默认	17:02:58.186055+0800	FitCount	Should send trait collection or coordinate space update, interface style 1 -> 1, <UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70)
默认	17:02:58.186247+0800	FitCount	Not push traits update to screen for new style 1, <UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70)
默认	17:02:58.186713+0800	FitCount	Initializing: <_UIHomeAffordanceSceneNotifier: 0x12a1ec380>; with scene: <UIWindowScene: 0x10283b750>
默认	17:02:58.186846+0800	FitCount	0x12a2ecab0 setDelegate:<0x12a2ecc00 _UIBacklightEnvironment> hasDelegate:YES for environment:sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:58.186990+0800	FitCount	Not push traits update to screen for new style 1, <UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70)
默认	17:02:58.250528+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:58.358686+0800	FitCount	policyStatus:<BKSHIDEventDeliveryPolicyObserver: 0x12a1c93b0; token: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70; status: ancestor> was:none
默认	17:02:58.358740+0800	FitCount	observerPolicyDidChange: 0x12a1c93b0 -> <_UIKeyWindowSceneObserver: 0x12a2ecd20>
默认	17:02:58.358795+0800	FitCount	Scene target of keyboard event deferring environment did change: 1; scene: UIWindowScene: 0x10283b750; scene identity: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:58.358853+0800	FitCount	[0x12a1ec690] Scene target of event deferring environments did update: scene: 0x10283b750; current systemShellManagesKeyboardFocus: 1; systemShellManagesKeyboardFocusForScene: 1; eligibleForRecordRemoval: 1;
默认	17:02:58.358903+0800	FitCount	Scene became target of keyboard event deferring environment: UIWindowScene: 0x10283b750; scene identity: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:58.359000+0800	FitCount	Stack[KeyWindow] 0x12a2eced0: Migrate scenes from LastOneWins -> SystemShellManaged
默认	17:02:58.359261+0800	FitCount	Setting default evaluation strategy for UIUserInterfaceIdiomPhone to SystemShellManaged
默认	17:02:58.190057+0800	FitCount	Realizing settings extension <_UIApplicationSceneDisplaySettings> on FBSSceneSettings
默认	17:02:58.191304+0800	FitCount	<UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70) Scene updated orientation preferences: none -> ( Pu Ll Lr )
默认	17:02:58.191358+0800	FitCount	Key window API is scene-level: YES
默认	17:02:58.191414+0800	FitCount	UIWindowScene: 0x10283b750: Window became key in scene: UIWindow: 0x102839820; contextId: 0x45E1D5CA: reason: UIWindowScene: 0x10283b750: Window requested to become key in scene: 0x102839820
默认	17:02:58.191471+0800	FitCount	Key window needs update: 1; currentKeyWindowScene: 0x0; evaluatedKeyWindowScene: 0x10283b750; currentApplicationKeyWindow: 0x0; evaluatedApplicationKeyWindow: 0x102839820; reason: UIWindowScene: 0x10283b750: Window requested to become key in scene: 0x102839820
默认	17:02:58.191553+0800	FitCount	Window did become application key: UIWindow: 0x102839820; contextId: 0x45E1D5CA; scene identity: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:58.191653+0800	FitCount	[0x12a1ec690] Begin local event deferring requested for token: 0x12a18c240; environments: 1; reason: UIWindowScene: 0x10283b750: Begin event deferring in keyboardFocus for window: 0x102839820
默认	17:02:58.192126+0800	FitCount	Not push traits update to screen for new style 1, <UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70)
默认	17:02:58.193037+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:58.193090+0800	FitCount	Ignoring already applied deactivation reason: 5; deactivation reasons: 2080
默认	17:02:58.193188+0800	FitCount	Deactivation reason added: 12; deactivation reasons: 2080 -> 6176; animating application lifecycle event: 1
默认	17:02:58.193375+0800	FitCount	Deactivation reason removed: 11; deactivation reasons: 6176 -> 4128; animating application lifecycle event: 1
默认	17:02:58.193681+0800	FitCount	[0x12a1c88c0] Session activated
默认	17:02:58.199954+0800	FitCount	Not observing PTDefaults on customer install.
默认	17:02:58.372550+0800	FitCount	<nw_activity 50:1 [F97600F2-4BEB-4965-B8A2-E540AFDA985A] (global parent) (reporting strategy default) complete (reason success)> complete with reason 2 (success), duration 183ms
默认	17:02:58.372840+0800	FitCount	<nw_activity 50:2 [F16474D4-AD21-43DE-A206-F1D23C2E9DF3] (reporting strategy default) complete (reason success)> complete with reason 2 (success), duration 182ms
默认	17:02:58.372992+0800	FitCount	Unsetting the global parent activity <nw_activity 50:1 [F97600F2-4BEB-4965-B8A2-E540AFDA985A] (global parent) (reporting strategy default) complete (reason success)>
默认	17:02:58.373039+0800	FitCount	Unset the global parent activity
默认	17:02:58.201375+0800	FitCount	[0x12a1d9cc0] activating connection: mach=true listener=false peer=false name=com.apple.fontservicesd
默认	17:02:58.214581+0800	FitCount	Create activity from XPC object <nw_activity 50:1 [F97600F2-4BEB-4965-B8A2-E540AFDA985A] (reporting strategy default)>
默认	17:02:58.214636+0800	FitCount	Create activity from XPC object <nw_activity 50:2 [F16474D4-AD21-43DE-A206-F1D23C2E9DF3] (reporting strategy default)>
默认	17:02:58.214684+0800	FitCount	Set activity <nw_activity 50:1 [F97600F2-4BEB-4965-B8A2-E540AFDA985A] (reporting strategy default)> as the global parent
默认	17:02:58.214788+0800	FitCount	AggregateDictionary is deprecated and has been removed. Please migrate to Core Analytics.
默认	17:02:58.214890+0800	FitCount	Ending background task with UIBackgroundTaskIdentifier: 1
默认	17:02:58.214946+0800	FitCount	Ending task with identifier 1 and description: <private>, _expireHandler: (null)
默认	17:02:58.215065+0800	FitCount	Decrementing reference count for assertion <private> (used by background task with identifier 1: <private>)
默认	17:02:58.215202+0800	FitCount	Will invalidate assertion: <BKSProcessAssertion: 0x12a1c81e0> for task identifier: 1
默认	17:02:58.215641+0800	FitCount	Target list changed: <CADisplay:LCD primary>
默认	17:02:58.219392+0800	FitCount	startConnection
默认	17:02:58.221168+0800	FitCount	[0x12ae303c0] activating connection: mach=true listener=false peer=false name=com.apple.UIKit.KeyboardManagement.hosted
默认	17:02:58.221298+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:58.223025+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:58.465648+0800	FitCount	Not push traits update to screen for new style 1, <UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70)
默认	17:02:58.466046+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:58.553378+0800	FitCount	Deactivation reason removed: 12; deactivation reasons: 4128 -> 32; animating application lifecycle event: 1
默认	17:02:58.553457+0800	FitCount	Send setDeactivating: N (-DeactivationReason:SuspendedEventsOnly)
默认	17:02:58.553512+0800	FitCount	Deactivation reason removed: 5; deactivation reasons: 32 -> 0; animating application lifecycle event: 0
默认	17:02:58.554193+0800	FitCount	Updating configuration of monitor M2951-1
默认	17:02:58.554820+0800	FitCount	[0x12a1d0d00] activating connection: mach=true listener=false peer=false name=com.apple.hangtracermonitor
默认	17:02:58.554914+0800	FitCount	Creating side-channel connection to com.apple.runningboard
默认	17:02:58.554965+0800	FitCount	[0x12a1d0e00] activating connection: mach=true listener=false peer=false name=com.apple.runningboard
默认	17:02:58.555092+0800	FitCount	Skip setting user action callback for 3rd party apps
默认	17:02:58.555183+0800	FitCount	[0x12a1d0f00] activating connection: mach=true listener=false peer=false name=com.apple.analyticsd
默认	17:02:58.555233+0800	FitCount	[0x12a1d0d00] invalidated because the current process cancelled the connection by calling xpc_connection_cancel()
默认	17:02:58.242002+0800	FitCount	handleKeyboardChange: set currentKeyboard:N (wasKeyboard:N)
默认	17:02:58.242209+0800	FitCount	forceReloadInputViews
默认	17:02:58.242259+0800	FitCount	Reloading input views for: <(null): 0x0; > force: 1
默认	17:02:58.242429+0800	FitCount	isWritingToolsHandlingKeyboardTracking:Y (WT ready:Y, Arbiter ready:Y)
默认	17:02:58.250551+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:58.358712+0800	FitCount	policyStatus:<BKSHIDEventDeliveryPolicyObserver: 0x12a1c93b0; token: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70; status: ancestor> was:none
默认	17:02:58.358763+0800	FitCount	observerPolicyDidChange: 0x12a1c93b0 -> <_UIKeyWindowSceneObserver: 0x12a2ecd20>
默认	17:02:58.358820+0800	FitCount	Scene target of keyboard event deferring environment did change: 1; scene: UIWindowScene: 0x10283b750; scene identity: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:58.358879+0800	FitCount	[0x12a1ec690] Scene target of event deferring environments did update: scene: 0x10283b750; current systemShellManagesKeyboardFocus: 1; systemShellManagesKeyboardFocusForScene: 1; eligibleForRecordRemoval: 1;
默认	17:02:58.358927+0800	FitCount	Scene became target of keyboard event deferring environment: UIWindowScene: 0x10283b750; scene identity: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:58.359184+0800	FitCount	Stack[KeyWindow] 0x12a2eced0: Migrate scenes from LastOneWins -> SystemShellManaged
默认	17:02:58.359311+0800	FitCount	Setting default evaluation strategy for UIUserInterfaceIdiomPhone to SystemShellManaged
默认	17:02:58.372606+0800	FitCount	<nw_activity 50:1 [F97600F2-4BEB-4965-B8A2-E540AFDA985A] (global parent) (reporting strategy default) complete (reason success)> complete with reason 2 (success), duration 183ms
默认	17:02:58.372867+0800	FitCount	<nw_activity 50:2 [F16474D4-AD21-43DE-A206-F1D23C2E9DF3] (reporting strategy default) complete (reason success)> complete with reason 2 (success), duration 182ms
默认	17:02:58.373014+0800	FitCount	Unsetting the global parent activity <nw_activity 50:1 [F97600F2-4BEB-4965-B8A2-E540AFDA985A] (global parent) (reporting strategy default) complete (reason success)>
默认	17:02:58.373162+0800	FitCount	Unset the global parent activity
默认	17:02:58.465790+0800	FitCount	Not push traits update to screen for new style 1, <UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70)
默认	17:02:58.466073+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:58.553426+0800	FitCount	Deactivation reason removed: 12; deactivation reasons: 4128 -> 32; animating application lifecycle event: 1
默认	17:02:58.553483+0800	FitCount	Send setDeactivating: N (-DeactivationReason:SuspendedEventsOnly)
默认	17:02:58.553538+0800	FitCount	Deactivation reason removed: 5; deactivation reasons: 32 -> 0; animating application lifecycle event: 0
默认	17:02:58.554238+0800	FitCount	Updating configuration of monitor M2951-1
默认	17:02:58.554846+0800	FitCount	[0x12a1d0d00] activating connection: mach=true listener=false peer=false name=com.apple.hangtracermonitor
默认	17:02:58.554936+0800	FitCount	Creating side-channel connection to com.apple.runningboard
默认	17:02:58.554990+0800	FitCount	[0x12a1d0e00] activating connection: mach=true listener=false peer=false name=com.apple.runningboard
默认	17:02:58.555114+0800	FitCount	Skip setting user action callback for 3rd party apps
默认	17:02:58.555207+0800	FitCount	[0x12a1d0f00] activating connection: mach=true listener=false peer=false name=com.apple.analyticsd
默认	17:02:58.555257+0800	FitCount	[0x12a1d0d00] invalidated because the current process cancelled the connection by calling xpc_connection_cancel()
默认	17:02:58.754609+0800	FitCount	Hit the server for a process handle c17fecc00000b87 that resolved to: [app<com.jack.FitCount(D179D11A-42C7-4416-A55B-8D29D4BDD786)>:2951]
默认	17:02:58.754690+0800	FitCount	Received state update for 2951 (app<com.jack.FitCount(D179D11A-42C7-4416-A55B-8D29D4BDD786)>, unknown-NotVisible
默认	17:02:58.754648+0800	FitCount	Hit the server for a process handle c17fecc00000b87 that resolved to: [app<com.jack.FitCount(D179D11A-42C7-4416-A55B-8D29D4BDD786)>:2951]
默认	17:02:58.754730+0800	FitCount	Received state update for 2951 (app<com.jack.FitCount(D179D11A-42C7-4416-A55B-8D29D4BDD786)>, unknown-NotVisible
默认	17:02:59.156615+0800	FitCount	Received state update for 2951 (app<com.jack.FitCount(D179D11A-42C7-4416-A55B-8D29D4BDD786)>, unknown-NotVisible
默认	17:02:59.233494+0800	FitCount	Received state update for 2951 (app<com.jack.FitCount(D179D11A-42C7-4416-A55B-8D29D4BDD786)>, unknown-NotVisible
默认	17:02:59.156637+0800	FitCount	Received state update for 2951 (app<com.jack.FitCount(D179D11A-42C7-4416-A55B-8D29D4BDD786)>, unknown-NotVisible
默认	17:02:59.233520+0800	FitCount	Received state update for 2951 (app<com.jack.FitCount(D179D11A-42C7-4416-A55B-8D29D4BDD786)>, unknown-NotVisible
默认	17:02:59.890887+0800	FitCount	TX focusApplication (peekAppEvent) stealKB:Y scene:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:59.891010+0800	FitCount	Evaluating dispatch of UIEvent: 0x12a21d960; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0
默认	17:02:59.891171+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to windows: 1
默认	17:02:59.890912+0800	FitCount	TX focusApplication (peekAppEvent) stealKB:Y scene:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:02:59.891036+0800	FitCount	Evaluating dispatch of UIEvent: 0x12a21d960; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0
默认	17:02:59.891200+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to windows: 1
默认	17:02:59.891369+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102839820>; contextId: 0x45E1D5CA
默认	17:02:59.891343+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102839820>; contextId: 0x45E1D5CA
默认	17:02:59.892080+0800	FitCount	Evaluating dispatch of UIEvent: 0x12a21d960; type: 0; subtype: 0; backing type: 11; shouldSend: 0; ignoreInteractionEvents: 0, systemGestureStateChange: 0
默认	17:02:59.892055+0800	FitCount	Evaluating dispatch of UIEvent: 0x12a21d960; type: 0; subtype: 0; backing type: 11; shouldSend: 0; ignoreInteractionEvents: 0, systemGestureStateChange: 0
默认	17:02:59.951919+0800	FitCount	Evaluating dispatch of UIEvent: 0x12a21d960; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0
默认	17:02:59.952023+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to windows: 1
默认	17:02:59.952126+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102839820>; contextId: 0x45E1D5CA
默认	17:02:59.951889+0800	FitCount	Evaluating dispatch of UIEvent: 0x12a21d960; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0
默认	17:02:59.951997+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to windows: 1
默认	17:02:59.952099+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102839820>; contextId: 0x45E1D5CA
默认	17:02:59.957217+0800	FitCount	Requesting container lookup; class = 13, identifier = <private>, group_identifier = <private>, create = 1, temp = 0, euid = 501, uid = 501
默认	17:02:59.957242+0800	FitCount	Requesting container lookup; class = 13, identifier = <private>, group_identifier = <private>, create = 1, temp = 0, euid = 501, uid = 501
默认	17:02:59.959316+0800	FitCount	container_query_get_single_result: success
默认	17:02:59.959908+0800	FitCount	container_system_group_path_for_identifier: success
默认	17:02:59.959964+0800	FitCount	Got system group container path from MCM for systemgroup.com.apple.configurationprofiles: /private/var/containers/Shared/SystemGroup/systemgroup.com.apple.configurationprofiles
默认	17:02:59.959556+0800	FitCount	container_query_get_single_result: success
默认	17:02:59.959938+0800	FitCount	container_system_group_path_for_identifier: success
默认	17:02:59.959997+0800	FitCount	Got system group container path from MCM for systemgroup.com.apple.configurationprofiles: /private/var/containers/Shared/SystemGroup/systemgroup.com.apple.configurationprofiles
默认	17:02:59.963013+0800	FitCount	[0x12a1d0d00] activating connection: mach=true listener=false peer=false name=com.apple.tccd
默认	17:02:59.963041+0800	FitCount	[0x12a1d0d00] activating connection: mach=true listener=false peer=false name=com.apple.tccd
默认	17:02:59.970398+0800	FitCount	[0x12a1d0d00] invalidated after the last release of the connection object
默认	17:02:59.972332+0800	FitCount	Evaluating dispatch of UIEvent: 0x12a21d960; type: 0; subtype: 0; backing type: 11; shouldSend: 0; ignoreInteractionEvents: 0, systemGestureStateChange: 1
默认	17:02:59.972486+0800	FitCount	ℹ️ [ExerciseSitupView.swift:45] init(exerciseName:): FirstDetailView初始化，exerciseName=
默认	17:02:59.970421+0800	FitCount	[0x12a1d0d00] invalidated after the last release of the connection object
默认	17:02:59.973337+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession init]: (0x12a2b4670) called
默认	17:02:59.972357+0800	FitCount	Evaluating dispatch of UIEvent: 0x12a21d960; type: 0; subtype: 0; backing type: 11; shouldSend: 0; ignoreInteractionEvents: 0, systemGestureStateChange: 1
默认	17:02:59.972508+0800	FitCount	ℹ️ [ExerciseSitupView.swift:45] init(exerciseName:): FirstDetailView初始化，exerciseName=
默认	17:02:59.978780+0800	FitCount	Pixel format registry initialized. Constant classes enabled.
默认	17:02:59.979876+0800	FitCount	[0x12ae30c80] activating connection: mach=true listener=false peer=false name=com.apple.lsd.mapdb
默认	17:02:59.973458+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession init]: (0x12a2b4670) called
默认	17:02:59.978812+0800	FitCount	Pixel format registry initialized. Constant classes enabled.
默认	17:02:59.979906+0800	FitCount	[0x12ae30c80] activating connection: mach=true listener=false peer=false name=com.apple.lsd.mapdb
默认	17:02:59.996283+0800	FitCount	[0x12a1d1300] activating connection: mach=true listener=false peer=false name=com.apple.coremedia.capturesource
默认	17:02:59.996212+0800	FitCount	[0x12a1d1300] activating connection: mach=true listener=false peer=false name=com.apple.coremedia.capturesource
默认	17:02:59.998307+0800	FitCount	[0x12a1d1200] activating connection: mach=true listener=false peer=false name=com.apple.coremedia.capturesession
默认	17:02:59.998764+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _initWithMediaEnvironment:]: (0x12a2b4670) finished
默认	17:02:59.998280+0800	FitCount	[0x12a1d1200] activating connection: mach=true listener=false peer=false name=com.apple.coremedia.capturesession
默认	17:02:59.998720+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _initWithMediaEnvironment:]: (0x12a2b4670) finished
默认	17:03:00.001624+0800	FitCount	ℹ️ [DeviceMotionManager.swift:167] checkAvailability(): 设备运动数据可用
默认	17:03:00.001599+0800	FitCount	ℹ️ [DeviceMotionManager.swift:167] checkAvailability(): 设备运动数据可用
默认	17:03:00.004090+0800	FitCount	ℹ️ [DeviceMotionManager.swift:57] startMotionUpdates(): 开始设备运动数据更新
默认	17:03:00.004319+0800	FitCount	ℹ️ [SitUpCounter.swift:112] init(): 设备运动管理器已启动
默认	17:03:00.004827+0800	FitCount	ℹ️ [CameraManager.swift:72] init(): CameraManager初始化
默认	17:03:00.004059+0800	FitCount	ℹ️ [DeviceMotionManager.swift:57] startMotionUpdates(): 开始设备运动数据更新
默认	17:03:00.005000+0800	FitCount	UseInterruptCal,platformDefault,<private>,overrideSet,<private>,final,<private>
默认	17:03:00.004295+0800	FitCount	ℹ️ [SitUpCounter.swift:112] init(): 设备运动管理器已启动
默认	17:03:00.004786+0800	FitCount	ℹ️ [CameraManager.swift:72] init(): CameraManager初始化
默认	17:03:00.005573+0800	FitCount	[CLIoHidInterface] Adding new Device with usage pair {65280, 41}
默认	17:03:00.004975+0800	FitCount	UseInterruptCal,platformDefault,<private>,overrideSet,<private>,final,<private>
默认	17:03:00.005548+0800	FitCount	[CLIoHidInterface] Adding new Device with usage pair {65280, 41}
默认	17:03:00.027704+0800	FitCount	[CLIoHidInterface] Event system client initialized successfully
默认	17:03:00.027726+0800	FitCount	[CLIoHidInterface] Event system client initialized successfully
默认	17:03:00.032061+0800	FitCount	{"msg":"#Cohorting CohortId assignment for silo", "Silo":"CLGeomagneticModelProviderSilo", "CohortId":""}
默认	17:03:00.032361+0800	FitCount	{"msg":"#Manufacturing service", "event":"activity", "RequestedServiceName":"CLGeomagneticModelProvider", "EffectiveServiceName":"CLGeomagneticModelProvider"}
默认	17:03:00.032485+0800	FitCount	{"msg":"#registerSelectorInfosAndValidateProtocolRecursively ", "protocolName":"CLGeomagneticModelProviderProtocol"}
默认	17:03:00.032624+0800	FitCount	{"msg":"#registerSelectorInfosAndValidateProtocolRecursively ", "protocolName":"CLNotifierServiceProtocol"}
默认	17:03:00.032773+0800	FitCount	{"msg":"#registerSelectorInfosAndValidateProtocolRecursively ", "protocolName":"CLGeomagneticModelProviderClientProtocol"}
默认	17:03:00.032845+0800	FitCount	{"msg":"#registerSelectorInfosAndValidateProtocolRecursively ", "protocolName":"CLNotifierServiceClientProtocol"}
默认	17:03:00.033570+0800	FitCount	Starting device motion, mode=0xf,useAccelerometer=1,useGyro=1,useCompass=1,fUseNorthRef=1,buildingGYTT=0
默认	17:03:00.033693+0800	FitCount	{"msg":"#Manufacturing service complete", "EffectiveServiceName":"CLGeomagneticModelProvider"}
默认	17:03:00.032128+0800	FitCount	{"msg":"#Cohorting CohortId assignment for silo", "Silo":"CLGeomagneticModelProviderSilo", "CohortId":""}
默认	17:03:00.032402+0800	FitCount	{"msg":"#Manufacturing service", "event":"activity", "RequestedServiceName":"CLGeomagneticModelProvider", "EffectiveServiceName":"CLGeomagneticModelProvider"}
默认	17:03:00.032516+0800	FitCount	{"msg":"#registerSelectorInfosAndValidateProtocolRecursively ", "protocolName":"CLGeomagneticModelProviderProtocol"}
默认	17:03:00.032660+0800	FitCount	{"msg":"#registerSelectorInfosAndValidateProtocolRecursively ", "protocolName":"CLNotifierServiceProtocol"}
默认	17:03:00.032810+0800	FitCount	{"msg":"#registerSelectorInfosAndValidateProtocolRecursively ", "protocolName":"CLGeomagneticModelProviderClientProtocol"}
默认	17:03:00.032877+0800	FitCount	{"msg":"#registerSelectorInfosAndValidateProtocolRecursively ", "protocolName":"CLNotifierServiceClientProtocol"}
默认	17:03:00.034830+0800	FitCount	[CLIoHidInterface] Adding new Device with usage pair {65292, 4}
默认	17:03:00.033595+0800	FitCount	Starting device motion, mode=0xf,useAccelerometer=1,useGyro=1,useCompass=1,fUseNorthRef=1,buildingGYTT=0
默认	17:03:00.033723+0800	FitCount	{"msg":"#Manufacturing service complete", "EffectiveServiceName":"CLGeomagneticModelProvider"}
默认	17:03:00.034854+0800	FitCount	[CLIoHidInterface] Adding new Device with usage pair {65292, 4}
默认	17:03:00.037184+0800	FitCount	[CLIoHidInterface] Service for device with usage pair {65292, 4} is not ready
默认	17:03:00.039216+0800	FitCount	[CLIoHidInterface] Service ref with usage pair {65292, 4} is <private> with matching properties <private>
默认	17:03:00.039452+0800	FitCount	[CLIoHidInterface] Property for usage pair {65292, 4}: {ReportInterval = 16667} was set successfully
默认	17:03:00.040121+0800	FitCount	[CLIoHidInterface] Service ref with usage pair {65292, 4} is <private> with matching properties <private>
默认	17:03:00.042067+0800	FitCount	[CLIoHidInterface] Property for usage pair {65292, 4}: {ReportInterval = 16667} was set successfully
默认	17:03:00.037209+0800	FitCount	[CLIoHidInterface] Service for device with usage pair {65292, 4} is not ready
默认	17:03:00.039241+0800	FitCount	[CLIoHidInterface] Service ref with usage pair {65292, 4} is <private> with matching properties <private>
默认	17:03:00.039474+0800	FitCount	[CLIoHidInterface] Property for usage pair {65292, 4}: {ReportInterval = 16667} was set successfully
默认	17:03:00.040148+0800	FitCount	[CLIoHidInterface] Service ref with usage pair {65292, 4} is <private> with matching properties <private>
默认	17:03:00.042089+0800	FitCount	[CLIoHidInterface] Property for usage pair {65292, 4}: {ReportInterval = 16667} was set successfully
默认	17:03:00.052503+0800	FitCount	Fast path has been initialized,serviceRef,0x12a1919a0
默认	17:03:00.086994+0800	FitCount	[CLSensorFusionService] q.x,<private>,q.y,<private>,q.z,<private>,q.w,<private>,userAccel.x,<private>,userAccel.y,<private>,userAccel.z,<private>,rotationRate.x,<private>,rotationRate.y,<private>,rotationRate.z,<private>,magneticField.x,<private>,magneticField.y,<private>,magneticField.z,<private>,magBiasEstVar.x,<private>,magBiasEstVar.y,<private>,magBiasEstVar.z,<private>,heading,<private>,accuracy,<private>,level,5,variant,15,mode,2,status,0x3704,clientID,0,timestamp,48958.517873,now,48958.520699,latency,0.002827
默认	17:03:00.052526+0800	FitCount	Fast path has been initialized,serviceRef,0x12a1919a0
默认	17:03:00.165072+0800	FitCount	<UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70) Scene updated orientation preferences: ( Pu Ll Lr ) -> ( Pu )
默认	17:03:00.087027+0800	FitCount	[CLSensorFusionService] q.x,<private>,q.y,<private>,q.z,<private>,q.w,<private>,userAccel.x,<private>,userAccel.y,<private>,userAccel.z,<private>,rotationRate.x,<private>,rotationRate.y,<private>,rotationRate.z,<private>,magneticField.x,<private>,magneticField.y,<private>,magneticField.z,<private>,magBiasEstVar.x,<private>,magBiasEstVar.y,<private>,magBiasEstVar.z,<private>,heading,<private>,accuracy,<private>,level,5,variant,15,mode,2,status,0x3704,clientID,0,timestamp,48958.517873,now,48958.520699,latency,0.002827
默认	17:03:00.184711+0800	FitCount	ℹ️ [ExerciseSitupView.swift:295] body: FirstDetailView显示
默认	17:03:00.184760+0800	FitCount	ℹ️ [CameraManager.swift:114] setupAndStartSession(): 开始设置并启动相机
默认	17:03:00.184854+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession beginConfiguration]: (0x12a2b4670)
默认	17:03:00.185014+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _beginConfiguration]: (0x12a2b4670) updated beginConfigRefCount 1
默认	17:03:00.185395+0800	FitCount	ℹ️ [CameraManager.swift:144] setupAndStartSession(): 已获得相机权限
默认	17:03:00.187798+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke_2: <private> changed... newDownplayUserPreferredCameraOverrideHistory = F (based on <private>), new user preferred camera history <private>
默认	17:03:00.187942+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: dispatching async to preferred camera property refresh queue
默认	17:03:00.188153+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _cameraHistoryDispatchQueue]_block_invoke: using main queue for refreshing camera properties
默认	17:03:00.188261+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: async dispatch to preferred camera property refresh queue returned
默认	17:03:00.188306+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: change handler for <private> returning
默认	17:03:00.188411+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: <private> changed... newDownplayUserPreferredCameraOverrideHistory = F (based on <private>), new user preferred camera override history <private>
默认	17:03:00.188458+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: dispatching async to preferred camera property refresh queue
默认	17:03:00.188504+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: async dispatch to preferred camera property refresh queue returned
默认	17:03:00.188550+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: change handler for <private> returning
默认	17:03:00.188705+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: <private> changed... new streaming camera history <private>
默认	17:03:00.188796+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: dispatching async to preferred camera property refresh queue
默认	17:03:00.188896+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: async dispatch to preferred camera property refresh queue returned
默认	17:03:00.188950+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: change handler for <private> returning
默认	17:03:00.189043+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: doing deferred dispatch async to preferred camera property refresh queue
默认	17:03:00.189097+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke_2: deferred async dispatch to preferred camera property refresh queue returned
默认	17:03:00.189444+0800	FitCount	<<<< AVCaptureDevice >>>> +[AVCaptureDevice _listEligibleEffects]_block_invoke: App with preferencesDomain '<private>' bundle '<private>' is NOT eligible for NSCameraBackgroundReplacementEnabled effects
默认	17:03:00.189491+0800	FitCount	<<<< AVCaptureDevice >>>> +[AVCaptureDevice _listEligibleEffects]_block_invoke: App with preferencesDomain '<private>' bundle '<private>' is NOT eligible for NSCameraPortraitEffectEnabled effects
默认	17:03:00.189544+0800	FitCount	<<<< AVCaptureDevice >>>> +[AVCaptureDevice _listEligibleEffects]_block_invoke: App with preferencesDomain '<private>' bundle '<private>' is NOT eligible for NSCameraStudioLightEnabled effects
默认	17:03:00.189590+0800	FitCount	<<<< AVCaptureDevice >>>> +[AVCaptureDevice _listEligibleEffects]_block_invoke: App with preferencesDomain '<private>' bundle '<private>' is NOT eligible for NSCameraReactionEffectsEnabled effects
默认	17:03:00.189830+0800	FitCount	<<<< AVCaptureDevice >>>> +[AVCaptureDevice reactionEffectGesturesEnabledDefault]_block_invoke: Using default from system 0
默认	17:03:00.189968+0800	FitCount	<<<< AVCaptureDevice >>>> +[AVCaptureDevice setUpReactionEffectsStateOnce]_block_invoke: Suppressed gestures 1 : enabled 0 have shown 0
默认	17:03:00.203437+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: getting current value for userPreferredCamera (allowsSuspendedPreferredCameras = F)
默认	17:03:00.165096+0800	FitCount	<UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70) Scene updated orientation preferences: ( Pu Ll Lr ) -> ( Pu )
默认	17:03:00.184735+0800	FitCount	ℹ️ [ExerciseSitupView.swift:295] body: FirstDetailView显示
默认	17:03:00.184783+0800	FitCount	ℹ️ [CameraManager.swift:114] setupAndStartSession(): 开始设置并启动相机
默认	17:03:00.184988+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession beginConfiguration]: (0x12a2b4670)
默认	17:03:00.185036+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _beginConfiguration]: (0x12a2b4670) updated beginConfigRefCount 1
默认	17:03:00.185420+0800	FitCount	ℹ️ [CameraManager.swift:144] setupAndStartSession(): 已获得相机权限
默认	17:03:00.187838+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke_2: <private> changed... newDownplayUserPreferredCameraOverrideHistory = F (based on <private>), new user preferred camera history <private>
默认	17:03:00.187968+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: dispatching async to preferred camera property refresh queue
默认	17:03:00.188183+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _cameraHistoryDispatchQueue]_block_invoke: using main queue for refreshing camera properties
默认	17:03:00.188282+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: async dispatch to preferred camera property refresh queue returned
默认	17:03:00.188328+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: change handler for <private> returning
默认	17:03:00.188435+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: <private> changed... newDownplayUserPreferredCameraOverrideHistory = F (based on <private>), new user preferred camera override history <private>
默认	17:03:00.188482+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: dispatching async to preferred camera property refresh queue
默认	17:03:00.188528+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: async dispatch to preferred camera property refresh queue returned
默认	17:03:00.188574+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: change handler for <private> returning
默认	17:03:00.188729+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: <private> changed... new streaming camera history <private>
默认	17:03:00.188819+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: dispatching async to preferred camera property refresh queue
默认	17:03:00.188920+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: async dispatch to preferred camera property refresh queue returned
默认	17:03:00.188971+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: change handler for <private> returning
默认	17:03:00.189074+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke: doing deferred dispatch async to preferred camera property refresh queue
默认	17:03:00.189163+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _setUpCameraHistoryOnce]_block_invoke_2: deferred async dispatch to preferred camera property refresh queue returned
默认	17:03:00.189466+0800	FitCount	<<<< AVCaptureDevice >>>> +[AVCaptureDevice _listEligibleEffects]_block_invoke: App with preferencesDomain '<private>' bundle '<private>' is NOT eligible for NSCameraBackgroundReplacementEnabled effects
默认	17:03:00.189521+0800	FitCount	<<<< AVCaptureDevice >>>> +[AVCaptureDevice _listEligibleEffects]_block_invoke: App with preferencesDomain '<private>' bundle '<private>' is NOT eligible for NSCameraPortraitEffectEnabled effects
默认	17:03:00.189568+0800	FitCount	<<<< AVCaptureDevice >>>> +[AVCaptureDevice _listEligibleEffects]_block_invoke: App with preferencesDomain '<private>' bundle '<private>' is NOT eligible for NSCameraStudioLightEnabled effects
默认	17:03:00.189615+0800	FitCount	<<<< AVCaptureDevice >>>> +[AVCaptureDevice _listEligibleEffects]_block_invoke: App with preferencesDomain '<private>' bundle '<private>' is NOT eligible for NSCameraReactionEffectsEnabled effects
默认	17:03:00.189851+0800	FitCount	<<<< AVCaptureDevice >>>> +[AVCaptureDevice reactionEffectGesturesEnabledDefault]_block_invoke: Using default from system 0
默认	17:03:00.189990+0800	FitCount	<<<< AVCaptureDevice >>>> +[AVCaptureDevice setUpReactionEffectsStateOnce]_block_invoke: Suppressed gestures 1 : enabled 0 have shown 0
默认	17:03:00.203459+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: getting current value for userPreferredCamera (allowsSuspendedPreferredCameras = F)
默认	17:03:00.756620+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: current list of devices:
默认	17:03:00.756648+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: current list of devices:
默认	17:03:00.756773+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.756797+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.756947+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757044+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757014+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757172+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757067+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757225+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757200+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757272+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757247+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757319+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.757296+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757366+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: devices with media types <private>:
默认	17:03:00.757342+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.757414+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757390+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: devices with media types <private>:
默认	17:03:00.757461+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757436+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757508+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757483+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757555+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757530+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757608+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757577+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757657+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757633+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757704+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.757680+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757762+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: 4. returning first available entry in streamingCameraHistory because it is a member of devicesWithMediaType and has suitable suspended state: <private>[F]
默认	17:03:00.757736+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.757818+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: getting current value for systemPreferredCamera (allowsSuspendedPreferredCameras = F)
默认	17:03:00.757791+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: 4. returning first available entry in streamingCameraHistory because it is a member of devicesWithMediaType and has suitable suspended state: <private>[F]
默认	17:03:00.757884+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: current list of devices:
默认	17:03:00.757858+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: getting current value for systemPreferredCamera (allowsSuspendedPreferredCameras = F)
默认	17:03:00.757931+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757906+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: current list of devices:
默认	17:03:00.758029+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.757956+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758085+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758061+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758132+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758110+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758183+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758157+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758230+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758207+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758276+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.758254+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758323+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: devices with media types <private>:
默认	17:03:00.758300+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.758371+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758347+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: devices with media types <private>:
默认	17:03:00.758480+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758394+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758544+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758594+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758642+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758739+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758833+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758928+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.759027+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: 4. returning first available entry in streamingCameraHistory because it is a member of devicesWithMediaType and has suitable suspended state: <private>[F]
默认	17:03:00.758572+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758620+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758716+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758811+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.758904+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.759000+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: 4. returning first available entry in streamingCameraHistory because it is a member of devicesWithMediaType and has suitable suspended state: <private>[F]
默认	17:03:00.762207+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: userPreferredCameraChanged T, resolved userPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>, current sUserPreferredCamera = (null)
默认	17:03:00.762255+0800	FitCount	ℹ️ [CameraManager.swift:330] configureSession(): 成功获取摄像头设备: 后置相机
默认	17:03:00.762306+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: systemPreferredCameraChanged T, resolved systemPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>, current sSystemPreferredCamera = (null)
默认	17:03:00.762353+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: changing sUserPreferredCamera from (null) to <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>
默认	17:03:00.762398+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: changing sSystemPreferredCamera from (null) to <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>
默认	17:03:00.762492+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: getting current value for userPreferredCamera (allowsSuspendedPreferredCameras = F)
默认	17:03:00.762537+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: current list of devices:
默认	17:03:00.762633+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.762679+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.762773+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.762818+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.762866+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.762912+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.762958+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.763006+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: devices with media types <private>:
默认	17:03:00.763051+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.763098+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.763144+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.763189+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.763235+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.763283+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession addInput:]: (0x12a2b4670) <AVCaptureDeviceInput: 0x12c9a3280 [后置相机]>
默认	17:03:00.763329+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.763374+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _beginConfiguration]: (0x12a2b4670) updated beginConfigRefCount 2
默认	17:03:00.763417+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.763469+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: 4. returning first available entry in streamingCameraHistory because it is a member of devicesWithMediaType and has suitable suspended state: <private>[F]
默认	17:03:00.763523+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: getting current value for systemPreferredCamera (allowsSuspendedPreferredCameras = F)
默认	17:03:00.762149+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: userPreferredCameraChanged T, resolved userPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>, current sUserPreferredCamera = (null)
默认	17:03:00.762232+0800	FitCount	ℹ️ [CameraManager.swift:330] configureSession(): 成功获取摄像头设备: 后置相机
默认	17:03:00.762279+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: systemPreferredCameraChanged T, resolved systemPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>, current sSystemPreferredCamera = (null)
默认	17:03:00.762331+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: changing sUserPreferredCamera from (null) to <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>
默认	17:03:00.762377+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: changing sSystemPreferredCamera from (null) to <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>
默认	17:03:00.762470+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: getting current value for userPreferredCamera (allowsSuspendedPreferredCameras = F)
默认	17:03:00.762516+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: current list of devices:
默认	17:03:00.762609+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.762656+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.762749+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.762795+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.762844+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.762890+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.762936+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.762980+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: devices with media types <private>:
默认	17:03:00.763030+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.763076+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.763120+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.763166+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.763211+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.763258+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession addInput:]: (0x12a2b4670) <AVCaptureDeviceInput: 0x12c9a3280 [后置相机]>
默认	17:03:00.763305+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.763351+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _beginConfiguration]: (0x12a2b4670) updated beginConfigRefCount 2
默认	17:03:00.763396+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.763444+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: 4. returning first available entry in streamingCameraHistory because it is a member of devicesWithMediaType and has suitable suspended state: <private>[F]
默认	17:03:00.763495+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: getting current value for systemPreferredCamera (allowsSuspendedPreferredCameras = F)
默认	17:03:00.763636+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: current list of devices:
默认	17:03:00.763720+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: current list of devices:
默认	17:03:00.763773+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.763833+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.763952+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764029+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764085+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764133+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764180+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.764230+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: devices with media types <private>:
默认	17:03:00.764276+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764323+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764370+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764417+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764466+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764528+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764578+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.764632+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: 4. returning first available entry in streamingCameraHistory because it is a member of devicesWithMediaType and has suitable suspended state: <private>[F]
默认	17:03:00.764686+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: userPreferredCameraChanged F, resolved userPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>, current sUserPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>
默认	17:03:00.764738+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: systemPreferredCameraChanged F, resolved systemPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>, current sSystemPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>
默认	17:03:00.764792+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: getting current value for userPreferredCamera (allowsSuspendedPreferredCameras = F)
默认	17:03:00.764846+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: current list of devices:
默认	17:03:00.764898+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764945+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764992+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765039+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765088+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765275+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.763748+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765322+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.763799+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.763866+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765445+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: devices with media types <private>:
默认	17:03:00.763993+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764060+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765575+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764110+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765644+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765692+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764158+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.765767+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764205+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: devices with media types <private>:
默认	17:03:00.765816+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764254+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765865+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764301+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765912+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.764348+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765964+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: 4. returning first available entry in streamingCameraHistory because it is a member of devicesWithMediaType and has suitable suspended state: <private>[F]
默认	17:03:00.764394+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.766048+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: getting current value for systemPreferredCamera (allowsSuspendedPreferredCameras = F)
默认	17:03:00.764441+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.766105+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: current list of devices:
默认	17:03:00.764498+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.766163+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764554+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.766211+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764604+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: 4. returning first available entry in streamingCameraHistory because it is a member of devicesWithMediaType and has suitable suspended state: <private>[F]
默认	17:03:00.766259+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764661+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: userPreferredCameraChanged F, resolved userPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>, current sUserPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>
默认	17:03:00.766307+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764714+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: systemPreferredCameraChanged F, resolved systemPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>, current sSystemPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>
默认	17:03:00.766355+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764763+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: getting current value for userPreferredCamera (allowsSuspendedPreferredCameras = F)
默认	17:03:00.766407+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.764822+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: current list of devices:
默认	17:03:00.766454+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.764873+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.766514+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _commitConfiguration]: (0x12a2b4670) updated beginConfigRefCount 1
默认	17:03:00.764921+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.766580+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: devices with media types <private>:
默认	17:03:00.764969+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.766657+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765017+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765064+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.766732+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765199+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.766813+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) called. skipConfig: 1
默认	17:03:00.765300+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.766864+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765404+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: devices with media types <private>:
默认	17:03:00.767072+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767136+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765546+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765607+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767246+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765669+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767297+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.765727+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765794+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767396+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: 4. returning first available entry in streamingCameraHistory because it is a member of devicesWithMediaType and has suitable suspended state: <private>[F]
默认	17:03:00.765841+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.765889+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.765937+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: 4. returning first available entry in streamingCameraHistory because it is a member of devicesWithMediaType and has suitable suspended state: <private>[F]
默认	17:03:00.767498+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: userPreferredCameraChanged F, resolved userPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>, current sUserPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>
默认	17:03:00.766001+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: getting current value for systemPreferredCamera (allowsSuspendedPreferredCameras = F)
默认	17:03:00.767545+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: systemPreferredCameraChanged F, resolved systemPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>, current sSystemPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>
默认	17:03:00.767596+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: getting current value for userPreferredCamera (allowsSuspendedPreferredCameras = F)
默认	17:03:00.766082+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: current list of devices:
默认	17:03:00.767642+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: current list of devices:
默认	17:03:00.766130+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767688+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.766187+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767734+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.766236+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767780+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.766283+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767824+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.766331+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767870+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.766383+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767916+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.766432+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.767963+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMaxFrameDurationInternal:]: MaxFrameDuration to set 1 / 30
默认	17:03:00.766478+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _commitConfiguration]: (0x12a2b4670) updated beginConfigRefCount 1
默认	17:03:00.768009+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.766554+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: devices with media types <private>:
默认	17:03:00.768059+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMinFrameDurationInternal:]: MinFrameDuration to set 1 / 30
默认	17:03:00.766603+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.768106+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: devices with media types <private>:
默认	17:03:00.766681+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.768153+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMinFrameDurationInternal:]: MinFrameDuration to set 1 / 30
默认	17:03:00.766774+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) called. skipConfig: 1
默认	17:03:00.768201+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.766839+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.768245+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMaxFrameDurationInternal:]: MaxFrameDuration to set 1 / 30
默认	17:03:00.768291+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767013+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.768337+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767098+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.768501+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767209+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767275+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.768598+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.768646+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767369+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: 4. returning first available entry in streamingCameraHistory because it is a member of devicesWithMediaType and has suitable suspended state: <private>[F]
默认	17:03:00.768691+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.767469+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: userPreferredCameraChanged F, resolved userPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>, current sUserPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>
默认	17:03:00.768790+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: 4. returning first available entry in streamingCameraHistory because it is a member of devicesWithMediaType and has suitable suspended state: <private>[F]
默认	17:03:00.767523+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: systemPreferredCameraChanged F, resolved systemPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>, current sSystemPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>
默认	17:03:00.768893+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: getting current value for systemPreferredCamera (allowsSuspendedPreferredCameras = F)
默认	17:03:00.767570+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: getting current value for userPreferredCamera (allowsSuspendedPreferredCameras = F)
默认	17:03:00.767621+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: current list of devices:
默认	17:03:00.768985+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: current list of devices:
默认	17:03:00.767666+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769031+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767710+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769076+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767756+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769165+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767802+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769212+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767848+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769258+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767894+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769304+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession addOutput:]: (0x12a2b4670) <AVCaptureVideoDataOutput: 0x12aeed6c0>
默认	17:03:00.769351+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.767940+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMaxFrameDurationInternal:]: MaxFrameDuration to set 1 / 30
默认	17:03:00.769399+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _beginConfiguration]: (0x12a2b4670) updated beginConfigRefCount 2
默认	17:03:00.767987+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.768037+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMinFrameDurationInternal:]: MinFrameDuration to set 1 / 30
默认	17:03:00.769443+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.768084+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: devices with media types <private>:
默认	17:03:00.769490+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: devices with media types <private>:
默认	17:03:00.769536+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.768131+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMinFrameDurationInternal:]: MinFrameDuration to set 1 / 30
默认	17:03:00.768177+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769582+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769627+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.768224+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMaxFrameDurationInternal:]: MaxFrameDuration to set 1 / 30
默认	17:03:00.768269+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769673+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.768315+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769718+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769764+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.768446+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769884+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.768574+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769942+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: 4. returning first available entry in streamingCameraHistory because it is a member of devicesWithMediaType and has suitable suspended state: <private>[F]
默认	17:03:00.768621+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769994+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: userPreferredCameraChanged F, resolved userPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>, current sUserPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>
默认	17:03:00.768668+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.770041+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: systemPreferredCameraChanged F, resolved systemPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>, current sSystemPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>
默认	17:03:00.768765+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: 4. returning first available entry in streamingCameraHistory because it is a member of devicesWithMediaType and has suitable suspended state: <private>[F]
默认	17:03:00.770121+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _commitConfiguration]: (0x12a2b4670) updated beginConfigRefCount 1
默认	17:03:00.770167+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) called. skipConfig: 1
默认	17:03:00.768865+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: getting current value for systemPreferredCamera (allowsSuspendedPreferredCameras = F)
默认	17:03:00.770220+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMinFrameDurationInternal:]: MinFrameDuration to set 1 / 30
默认	17:03:00.768964+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: current list of devices:
默认	17:03:00.770270+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMaxFrameDurationInternal:]: MaxFrameDuration to set 1 / 30
默认	17:03:00.770317+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _beginConfiguration]: (0x12a2b4670) updated beginConfigRefCount 2
默认	17:03:00.769009+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.770360+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _commitConfiguration]: (0x12a2b4670) updated beginConfigRefCount 1
默认	17:03:00.769053+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.770406+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) called. skipConfig: 1
默认	17:03:00.769144+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769190+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769234+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769282+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession addOutput:]: (0x12a2b4670) <AVCaptureVideoDataOutput: 0x12aeed6c0>
默认	17:03:00.770617+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveFormat:resetVideoZoomFactorAndMinMaxFrameDurations:sessionPreset:]: format:<AVCaptureDeviceFormat: 0x12a2b5fc0 'vide'/'420f' 1920x1080, { 1- 30 fps}, photo dims:{1920x1080,4224x2376}, fov:70.291, supports vis (max strength:Ultra), max zoom:123.75 (upscales @2.00), system zoom range:1.0-3.0, AF System:2, ISO:34.0-3264.0, SS:0.000015-1.000000, system exposure bias range:-2.0-2.0, supports HDR, supports wide color, supports multicam, supports high photo quality, supports CS RoI> preset:AVCaptureSessionPresetHigh
默认	17:03:00.769329+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769376+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _beginConfiguration]: (0x12a2b4670) updated beginConfigRefCount 2
默认	17:03:00.770664+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMinFrameDurationInternal:]: MinFrameDuration to set 1 / 30
默认	17:03:00.769423+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.770710+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMaxFrameDurationInternal:]: MaxFrameDuration to set 1 / 30
默认	17:03:00.769467+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: devices with media types <private>:
默认	17:03:00.769512+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769558+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769604+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769650+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.770977+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _addVideoPreviewLayer:exceptionReason:]: (0x12a2b4670) <AVCaptureVideoPreviewLayer: 0x12c9f2bb0>
默认	17:03:00.769695+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.771024+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession beginConfiguration]: (0x12a2b4670)
默认	17:03:00.771070+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _beginConfiguration]: (0x12a2b4670) updated beginConfigRefCount 2
默认	17:03:00.769742+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: <private>
默认	17:03:00.769825+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: -------------
默认	17:03:00.771133+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession commitConfiguration]: (0x12a2b4670)
默认	17:03:00.769908+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _defaultDeviceWithDeviceType:mediaTypes:position:cameraOverrideHistoryAllowed:cameraOverrideHistorySuspendedAllowed:wombatsMustBeMagic:userPreferredCameraHistorySuspendedAllowed:invalidSpecialDeviceTypes:]: 4. returning first available entry in streamingCameraHistory because it is a member of devicesWithMediaType and has suitable suspended state: <private>[F]
默认	17:03:00.771178+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _commitConfiguration]: (0x12a2b4670) updated beginConfigRefCount 1
默认	17:03:00.769968+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: userPreferredCameraChanged F, resolved userPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>, current sUserPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>
默认	17:03:00.771223+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) called. skipConfig: 1
默认	17:03:00.770018+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> +[AVCaptureFigVideoDevice _refreshPreferredCameraProperties:]: systemPreferredCameraChanged F, resolved systemPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>, current sSystemPreferredCamera = <AVCaptureFigVideoDevice: 0x12a2c0600 [后置相机][com.apple.avfoundation.avcapturedevice.built-in_video:0]>
默认	17:03:00.771269+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMinFrameDurationInternal:]: MinFrameDuration to set 1 / 30
默认	17:03:00.770098+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _commitConfiguration]: (0x12a2b4670) updated beginConfigRefCount 1
默认	17:03:00.771315+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMaxFrameDurationInternal:]: MaxFrameDuration to set 1 / 30
默认	17:03:00.770144+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) called. skipConfig: 1
默认	17:03:00.770191+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMinFrameDurationInternal:]: MinFrameDuration to set 1 / 30
默认	17:03:00.770246+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMaxFrameDurationInternal:]: MaxFrameDuration to set 1 / 30
默认	17:03:00.770293+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _beginConfiguration]: (0x12a2b4670) updated beginConfigRefCount 2
默认	17:03:00.771625+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession commitConfiguration]: (0x12a2b4670)
默认	17:03:00.771670+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _commitConfiguration]: (0x12a2b4670) updated beginConfigRefCount 0
默认	17:03:00.770339+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _commitConfiguration]: (0x12a2b4670) updated beginConfigRefCount 1
默认	17:03:00.770384+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) called. skipConfig: 1
默认	17:03:00.771714+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) called. skipConfig: 0
默认	17:03:00.771769+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMinFrameDurationInternal:]: MinFrameDuration to set 1 / 30
默认	17:03:00.771816+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMaxFrameDurationInternal:]: MaxFrameDuration to set 1 / 30
默认	17:03:00.770589+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveFormat:resetVideoZoomFactorAndMinMaxFrameDurations:sessionPreset:]: format:<AVCaptureDeviceFormat: 0x12a2b5fc0 'vide'/'420f' 1920x1080, { 1- 30 fps}, photo dims:{1920x1080,4224x2376}, fov:70.291, supports vis (max strength:Ultra), max zoom:123.75 (upscales @2.00), system zoom range:1.0-3.0, AF System:2, ISO:34.0-3264.0, SS:0.000015-1.000000, system exposure bias range:-2.0-2.0, supports HDR, supports wide color, supports multicam, supports high photo quality, supports CS RoI> preset:AVCaptureSessionPresetHigh
默认	17:03:00.770642+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMinFrameDurationInternal:]: MinFrameDuration to set 1 / 30
默认	17:03:00.770689+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMaxFrameDurationInternal:]: MaxFrameDuration to set 1 / 30
默认	17:03:00.770955+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _addVideoPreviewLayer:exceptionReason:]: (0x12a2b4670) <AVCaptureVideoPreviewLayer: 0x12c9f2bb0>
默认	17:03:00.771002+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession beginConfiguration]: (0x12a2b4670)
默认	17:03:00.771048+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _beginConfiguration]: (0x12a2b4670) updated beginConfigRefCount 2
默认	17:03:00.771111+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession commitConfiguration]: (0x12a2b4670)
默认	17:03:00.771156+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _commitConfiguration]: (0x12a2b4670) updated beginConfigRefCount 1
默认	17:03:00.771200+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) called. skipConfig: 1
默认	17:03:00.772732+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) New fcs config(1)
默认	17:03:00.771246+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMinFrameDurationInternal:]: MinFrameDuration to set 1 / 30
默认	17:03:00.772784+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) Setting fcs config(1) FigCaptureSessionConfiguration 0x12c85e440: ID 1, AVCaptureSessionPresetHigh multiCam: 0, appAudio: 1, autoConfig: 1, mixesWithOthers: 0, runWhileMultitasking: 0, checkIfFileAlreadyExistForMFO: 1
	VC 0x12c938e40: <SRC:Wide back 420f/1920x1080, 30-30(max:30), Z:1.00, ICM:0, (FD E:0 B:0 S:0), HR:1, GS: 1, FaceDrivenAEAFMode:3, FaceDrivenAEAFEnabledByDefault:1, cameraMountedInLandscape: YES> -> <SINK 0x12c9f3180:VideoData discards:1, preview:0, stability:0, requestedBufferAttachments.count:0>, BGRA/1920x1080, E:1, VIS:0, M:0, O:Landscape Right, DOC:0, RBC:12, CIM:0
	VC 0x12c938c00: <SRC:Wide back 420f/1920x1080, 30-30(max:30), Z:1.00, ICM:0, (FD E:0 B:0 S:0), HR:1, GS: 1, FaceDrivenAEAFMode:3, FaceDrivenAEAFEnabledByDefault:1, cameraMountedInLandscape: YES> -> <SINK 0x12ca15650:VideoPreview depth:0 filters:0 [] PrimaryCaptureRectModification:0, Aspect:0.000:1, Center:0.500 0.500, UniqueID:0 ZoomPIP:0 PortraitAutoSuggest:0>, /0x0, E:1, VIS:
默认	17:03:00.771292+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMaxFrameDurationInternal:]: MaxFrameDuration to set 1 / 30
默认	17:03:00.771600+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession commitConfiguration]: (0x12a2b4670)
默认	17:03:00.771647+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _commitConfiguration]: (0x12a2b4670) updated beginConfigRefCount 0
默认	17:03:00.771692+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) called. skipConfig: 0
默认	17:03:00.771744+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMinFrameDurationInternal:]: MinFrameDuration to set 1 / 30
默认	17:03:00.771794+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMaxFrameDurationInternal:]: MaxFrameDuration to set 1 / 30
错误	17:03:00.774034+0800	FitCount	⚠️ [CameraPreview.swift:47] previewLayer: 预览层关联的会话未运行
默认	17:03:00.772707+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) New fcs config(1)
默认	17:03:00.772758+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) Setting fcs config(1) FigCaptureSessionConfiguration 0x12c85e440: ID 1, AVCaptureSessionPresetHigh multiCam: 0, appAudio: 1, autoConfig: 1, mixesWithOthers: 0, runWhileMultitasking: 0, checkIfFileAlreadyExistForMFO: 1
	VC 0x12c938e40: <SRC:Wide back 420f/1920x1080, 30-30(max:30), Z:1.00, ICM:0, (FD E:0 B:0 S:0), HR:1, GS: 1, FaceDrivenAEAFMode:3, FaceDrivenAEAFEnabledByDefault:1, cameraMountedInLandscape: YES> -> <SINK 0x12c9f3180:VideoData discards:1, preview:0, stability:0, requestedBufferAttachments.count:0>, BGRA/1920x1080, E:1, VIS:0, M:0, O:Landscape Right, DOC:0, RBC:12, CIM:0
	VC 0x12c938c00: <SRC:Wide back 420f/1920x1080, 30-30(max:30), Z:1.00, ICM:0, (FD E:0 B:0 S:0), HR:1, GS: 1, FaceDrivenAEAFMode:3, FaceDrivenAEAFEnabledByDefault:1, cameraMountedInLandscape: YES> -> <SINK 0x12ca15650:VideoPreview depth:0 filters:0 [] PrimaryCaptureRectModification:0, Aspect:0.000:1, Center:0.500 0.500, UniqueID:0 ZoomPIP:0 PortraitAutoSuggest:0>, /0x0, E:1, VIS:
错误	17:03:00.774010+0800	FitCount	⚠️ [CameraPreview.swift:47] previewLayer: 预览层关联的会话未运行
默认	17:03:00.826678+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession startRunning]: (0x12a2b4670) (pthread:0x16db87000)
默认	17:03:00.826831+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) called. skipConfig: 0
默认	17:03:00.827617+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMinFrameDurationInternal:]: MinFrameDuration to set 1 / 30
默认	17:03:00.834013+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMaxFrameDurationInternal:]: MaxFrameDuration to set 1 / 30
默认	17:03:00.834615+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) New fcs config(2)
默认	17:03:00.826705+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession startRunning]: (0x12a2b4670) (pthread:0x16db87000)
默认	17:03:00.826853+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) called. skipConfig: 0
默认	17:03:00.827644+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMinFrameDurationInternal:]: MinFrameDuration to set 1 / 30
默认	17:03:00.834038+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMaxFrameDurationInternal:]: MaxFrameDuration to set 1 / 30
默认	17:03:00.834639+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) New fcs config(2)
默认	17:03:01.120245+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 7.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.120294+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 7.697941182295919°, 平滑后角度: 7.697941182295919°
默认	17:03:01.120734+0800	FitCount	ℹ️ [SitUpCounter.swift:337] processStateTransition(with:): 状态变化: ready -> lying_down, 角度: 7.697941182295919°
默认	17:03:01.120789+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 7.697941182295919°, 稳定帧数: 1/3
默认	17:03:01.141202+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 6.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.141248+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 6.746578085137433°, 平滑后角度: 7.222259633716676°
默认	17:03:01.141725+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 7.222259633716676°, 稳定帧数: 2/3
默认	17:03:01.176903+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 5.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.177004+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 5.817472287112072°, 平滑后角度: 6.753997184848475°
默认	17:03:01.177885+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 6.753997184848475°, 稳定帧数: 3/3
默认	17:03:01.208616+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 5.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.208666+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 4.9774938889895655°, 平滑后角度: 6.309871360883747°
默认	17:03:01.209352+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 6.309871360883747°, 稳定帧数: 4/3
默认	17:03:01.241919+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 4.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.241966+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 4.0681260078708865°, 平滑后角度: 5.861522290281175°
默认	17:03:01.244237+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 5.861522290281175°, 稳定帧数: 5/3
默认	17:03:01.120270+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 7.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.120317+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 7.697941182295919°, 平滑后角度: 7.697941182295919°
默认	17:03:01.120759+0800	FitCount	ℹ️ [SitUpCounter.swift:337] processStateTransition(with:): 状态变化: ready -> lying_down, 角度: 7.697941182295919°
默认	17:03:01.120811+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 7.697941182295919°, 稳定帧数: 1/3
默认	17:03:01.141226+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 6.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.141272+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 6.746578085137433°, 平滑后角度: 7.222259633716676°
默认	17:03:01.141751+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 7.222259633716676°, 稳定帧数: 2/3
默认	17:03:01.176925+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 5.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.177026+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 5.817472287112072°, 平滑后角度: 6.753997184848475°
默认	17:03:01.177908+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 6.753997184848475°, 稳定帧数: 3/3
默认	17:03:01.275679+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 3.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.275723+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 3.1470255299685768°, 平滑后角度: 4.951339159815706°
默认	17:03:01.276087+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 4.951339159815706°, 稳定帧数: 6/3
默认	17:03:01.208638+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 5.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.208689+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 4.9774938889895655°, 平滑后角度: 6.309871360883747°
默认	17:03:01.209381+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 6.309871360883747°, 稳定帧数: 4/3
默认	17:03:01.307934+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.307979+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 2.443268146119802°, 平滑后角度: 4.090677172012181°
默认	17:03:01.308393+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 4.090677172012181°, 稳定帧数: 7/3
默认	17:03:01.241942+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 4.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.241988+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 4.0681260078708865°, 平滑后角度: 5.861522290281175°
默认	17:03:01.244265+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 5.861522290281175°, 稳定帧数: 5/3
默认	17:03:01.341399+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.341445+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.792287608716968°, 平滑后角度: 3.2856402363331596°
默认	17:03:01.341703+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 3.2856402363331596°, 稳定帧数: 8/3
默认	17:03:01.275701+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 3.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.275747+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 3.1470255299685768°, 平滑后角度: 4.951339159815706°
默认	17:03:01.276113+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 4.951339159815706°, 稳定帧数: 6/3
默认	17:03:01.307955+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.308001+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 2.443268146119802°, 平滑后角度: 4.090677172012181°
默认	17:03:01.308419+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 4.090677172012181°, 稳定帧数: 7/3
默认	17:03:01.341423+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.341468+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.792287608716968°, 平滑后角度: 3.2856402363331596°
默认	17:03:01.341731+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 3.2856402363331596°, 稳定帧数: 8/3
默认	17:03:01.375870+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.375917+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.5671641686644313°, 平滑后角度: 2.603574292268133°
默认	17:03:01.376270+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.603574292268133°, 稳定帧数: 9/3
默认	17:03:01.375895+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.375978+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.5671641686644313°, 平滑后角度: 2.603574292268133°
默认	17:03:01.376296+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.603574292268133°, 稳定帧数: 9/3
默认	17:03:01.407485+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.407541+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.3606360398152284°, 平滑后角度: 2.0620762986570016°
默认	17:03:01.407939+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.0620762986570016°, 稳定帧数: 10/3
默认	17:03:01.442444+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.442492+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.2128476523958431°, 平滑后角度: 1.6752407231424542°
默认	17:03:01.442920+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.6752407231424542°, 稳定帧数: 11/3
默认	17:03:01.475183+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.475229+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.8013336721264327°, 平滑后角度: 1.3468538283437808°
默认	17:03:01.475517+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.3468538283437808°, 稳定帧数: 12/3
默认	17:03:01.507690+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.507736+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.16132394526751836°, 平滑后角度: 1.0206610956538906°
默认	17:03:01.508112+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.0206610956538906°, 稳定帧数: 13/3
默认	17:03:01.407517+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.407563+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.3606360398152284°, 平滑后角度: 2.0620762986570016°
默认	17:03:01.407963+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.0620762986570016°, 稳定帧数: 10/3
默认	17:03:01.541960+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.542037+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.3622728726619162°, 平滑后角度: 0.7796828364533879°
默认	17:03:01.542638+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.7796828364533879°, 稳定帧数: 14/3
默认	17:03:01.442467+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.442532+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.2128476523958431°, 平滑后角度: 1.6752407231424542°
默认	17:03:01.442945+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.6752407231424542°, 稳定帧数: 11/3
默认	17:03:01.475207+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.475251+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.8013336721264327°, 平滑后角度: 1.3468538283437808°
默认	17:03:01.475541+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.3468538283437808°, 稳定帧数: 12/3
默认	17:03:01.507714+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.507761+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.16132394526751836°, 平滑后角度: 1.0206610956538906°
默认	17:03:01.508130+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.0206610956538906°, 稳定帧数: 13/3
默认	17:03:01.541997+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.542059+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.3622728726619162°, 平滑后角度: 0.7796828364533879°
默认	17:03:01.542660+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.7796828364533879°, 稳定帧数: 14/3
默认	17:03:01.577265+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.577368+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.0488819419765707°, 平滑后角度: 0.7173320168856562°
默认	17:03:01.581810+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.7173320168856562°, 稳定帧数: 15/3
默认	17:03:01.607214+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.607260+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.34432087317510707°, 平滑后角度: 0.543626661041509°
默认	17:03:01.607598+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.543626661041509°, 稳定帧数: 16/3
默认	17:03:01.641117+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.641163+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.5832220351415502°, 平滑后角度: 0.7000043336445325°
默认	17:03:01.641392+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.7000043336445325°, 稳定帧数: 17/3
默认	17:03:01.675368+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.675414+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 2.363430695084701°, 平滑后角度: 1.1404256836079691°
默认	17:03:01.675636+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1404256836079691°, 稳定帧数: 18/3
默认	17:03:01.577289+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.577389+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.0488819419765707°, 平滑后角度: 0.7173320168856562°
默认	17:03:01.581835+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.7173320168856562°, 稳定帧数: 15/3
默认	17:03:01.709063+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.709109+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 2.4921696266334394°, 平滑后角度: 1.5664050344022737°
默认	17:03:01.709453+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.5664050344022737°, 稳定帧数: 19/3
默认	17:03:01.740797+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.740844+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 2.3767558403249955°, 平滑后角度: 1.8319798140719583°
默认	17:03:01.741246+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.8319798140719583°, 稳定帧数: 20/3
默认	17:03:01.607238+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.607284+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.34432087317510707°, 平滑后角度: 0.543626661041509°
默认	17:03:01.607625+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.543626661041509°, 稳定帧数: 16/3
默认	17:03:01.641139+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.641185+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.5832220351415502°, 平滑后角度: 0.7000043336445325°
默认	17:03:01.641421+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.7000043336445325°, 稳定帧数: 17/3
默认	17:03:01.675390+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.675436+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 2.363430695084701°, 平滑后角度: 1.1404256836079691°
默认	17:03:01.675659+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1404256836079691°, 稳定帧数: 18/3
默认	17:03:01.773720+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.773766+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 2.2436679123757943°, 平滑后角度: 2.2118492219120958°
默认	17:03:01.774240+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.2118492219120958°, 稳定帧数: 21/3
默认	17:03:01.709085+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.709131+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 2.4921696266334394°, 平滑后角度: 1.5664050344022737°
默认	17:03:01.709470+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.5664050344022737°, 稳定帧数: 19/3
默认	17:03:01.807873+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.807921+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.9579012856899174°, 平滑后角度: 2.2867850720217695°
默认	17:03:01.808136+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.2867850720217695°, 稳定帧数: 22/3
默认	17:03:01.841022+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.9° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.841067+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.941908315170594°, 平滑后角度: 2.2024805960389484°
默认	17:03:01.841501+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.2024805960389484°, 稳定帧数: 23/3
默认	17:03:01.740820+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.740866+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 2.3767558403249955°, 平滑后角度: 1.8319798140719583°
默认	17:03:01.741274+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.8319798140719583°, 稳定帧数: 20/3
默认	17:03:01.876811+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.9° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.876862+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.91030745225658°, 平滑后角度: 2.086108161163576°
默认	17:03:01.878575+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.086108161163576°, 稳定帧数: 24/3
默认	17:03:01.914148+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.914211+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.7779280645368258°, 平滑后角度: 1.9663426060059421°
默认	17:03:01.914673+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.9663426060059421°, 稳定帧数: 25/3
默认	17:03:01.773744+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.773788+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 2.2436679123757943°, 平滑后角度: 2.2118492219120958°
默认	17:03:01.774270+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.2118492219120958°, 稳定帧数: 21/3
默认	17:03:01.807898+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.807945+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.9579012856899174°, 平滑后角度: 2.2867850720217695°
默认	17:03:01.808160+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.2867850720217695°, 稳定帧数: 22/3
默认	17:03:01.980777+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.980823+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.6794145150498014°, 平滑后角度: 1.853491926540744°
默认	17:03:01.981439+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.853491926540744°, 稳定帧数: 26/3
默认	17:03:01.995501+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.995546+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.6241090485805239°, 平滑后角度: 1.786733479118865°
默认	17:03:01.995759+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.786733479118865°, 稳定帧数: 27/3
默认	17:03:01.841044+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.9° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.841089+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.941908315170594°, 平滑后角度: 2.2024805960389484°
默认	17:03:01.841528+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.2024805960389484°, 稳定帧数: 23/3
默认	17:03:02.067548+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.067661+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.5799205153780858°, 平滑后角度: 1.7143359191603635°
默认	17:03:02.067987+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.7143359191603635°, 稳定帧数: 28/3
默认	17:03:01.876835+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.9° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.876888+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.91030745225658°, 平滑后角度: 2.086108161163576°
默认	17:03:01.878598+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.086108161163576°, 稳定帧数: 24/3
默认	17:03:02.078834+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.078882+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.3840043373953543°, 平滑后角度: 1.6090752961881183°
默认	17:03:02.080122+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.6090752961881183°, 稳定帧数: 29/3
默认	17:03:02.094738+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.094784+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.4139448586909484°, 平滑后角度: 1.536278655018943°
默认	17:03:02.095052+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.536278655018943°, 稳定帧数: 30/3
默认	17:03:01.914181+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.914237+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.7779280645368258°, 平滑后角度: 1.9663426060059421°
默认	17:03:01.914716+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.9663426060059421°, 稳定帧数: 25/3
默认	17:03:02.129739+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.129789+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.1777737216347883°, 平滑后角度: 1.4359504963359402°
默认	17:03:02.130321+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.4359504963359402°, 稳定帧数: 31/3
默认	17:03:01.980799+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.980846+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.6794145150498014°, 平滑后角度: 1.853491926540744°
默认	17:03:01.981462+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.853491926540744°, 稳定帧数: 26/3
默认	17:03:01.995522+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:01.995568+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.6241090485805239°, 平滑后角度: 1.786733479118865°
默认	17:03:01.995781+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.786733479118865°, 稳定帧数: 27/3
默认	17:03:02.067572+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.067683+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.5799205153780858°, 平滑后角度: 1.7143359191603635°
默认	17:03:02.068011+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.7143359191603635°, 稳定帧数: 28/3
默认	17:03:02.264925+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.264975+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.3089142699637957°, 平滑后角度: 1.3729115406125945°
默认	17:03:02.267359+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.3729115406125945°, 稳定帧数: 32/3
默认	17:03:02.078856+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.078906+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.3840043373953543°, 平滑后角度: 1.6090752961881183°
默认	17:03:02.291743+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.291791+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.3801666823430732°, 平滑后角度: 1.332960774005592°
默认	17:03:02.292005+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.332960774005592°, 稳定帧数: 33/3
默认	17:03:02.080147+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.6090752961881183°, 稳定帧数: 29/3
默认	17:03:02.094760+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.094806+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.4139448586909484°, 平滑后角度: 1.536278655018943°
默认	17:03:02.095077+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.536278655018943°, 稳定帧数: 30/3
默认	17:03:02.309396+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.309448+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.715596672706993°, 平滑后角度: 1.3992792410679198°
默认	17:03:02.309681+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.3992792410679198°, 稳定帧数: 34/3
默认	17:03:02.129764+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.129812+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.1777737216347883°, 平滑后角度: 1.4359504963359402°
默认	17:03:02.130350+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.4359504963359402°, 稳定帧数: 31/3
默认	17:03:02.264949+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.264997+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.3089142699637957°, 平滑后角度: 1.3729115406125945°
默认	17:03:02.267383+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.3729115406125945°, 稳定帧数: 32/3
默认	17:03:02.341006+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.341053+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.8090862167305122°, 平滑后角度: 1.4783075126758325°
默认	17:03:02.341439+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.4783075126758325°, 稳定帧数: 35/3
默认	17:03:02.361591+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.9° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.361637+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.8956118796001504°, 平滑后角度: 1.6218751442689048°
默认	17:03:02.361849+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.6218751442689048°, 稳定帧数: 36/3
默认	17:03:02.374452+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.374497+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 2.03893591806818°, 平滑后角度: 1.7678794738897818°
默认	17:03:02.374814+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.7678794738897818°, 稳定帧数: 37/3
默认	17:03:02.291768+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.291815+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.3801666823430732°, 平滑后角度: 1.332960774005592°
默认	17:03:02.292027+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.332960774005592°, 稳定帧数: 33/3
默认	17:03:02.309422+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.309470+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.715596672706993°, 平滑后角度: 1.3992792410679198°
默认	17:03:02.398238+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.309705+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.3992792410679198°, 稳定帧数: 34/3
默认	17:03:02.398286+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 2.203721991558152°, 平滑后角度: 1.9325905357327975°
默认	17:03:02.398751+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.9325905357327975°, 稳定帧数: 38/3
默认	17:03:02.422847+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.422897+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 2.1013980000463715°, 平滑后角度: 2.0097508012006733°
默认	17:03:02.423254+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.0097508012006733°, 稳定帧数: 39/3
默认	17:03:02.341029+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.341078+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.8090862167305122°, 平滑后角度: 1.4783075126758325°
默认	17:03:02.341461+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.4783075126758325°, 稳定帧数: 35/3
默认	17:03:02.446438+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.446489+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 2.1229698007472364°, 平滑后角度: 2.0725275180040184°
默认	17:03:02.446711+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.0725275180040184°, 稳定帧数: 40/3
默认	17:03:02.361613+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.9° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.361659+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.8956118796001504°, 平滑后角度: 1.6218751442689048°
默认	17:03:02.361913+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.6218751442689048°, 稳定帧数: 36/3
默认	17:03:02.457968+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.458030+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.9599532491802225°, 平滑后角度: 2.0853957919200323°
默认	17:03:02.458400+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.0853957919200323°, 稳定帧数: 41/3
默认	17:03:02.374475+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.374520+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 2.03893591806818°, 平滑后角度: 1.7678794738897818°
默认	17:03:02.374882+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.7678794738897818°, 稳定帧数: 37/3
默认	17:03:02.552254+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.552300+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.829068827493363°, 平滑后角度: 2.043422373805069°
默认	17:03:02.552530+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.043422373805069°, 稳定帧数: 42/3
默认	17:03:02.398262+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.398308+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 2.203721991558152°, 平滑后角度: 1.9325905357327975°
默认	17:03:02.398776+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.9325905357327975°, 稳定帧数: 38/3
默认	17:03:02.570641+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.570687+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.7827756767752037°, 平滑后角度: 1.9592331108484795°
默认	17:03:02.570928+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.9592331108484795°, 稳定帧数: 43/3
默认	17:03:02.583914+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.583961+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.7687986660390815°, 平滑后角度: 1.8927132440470213°
默认	17:03:02.584300+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.8927132440470213°, 稳定帧数: 44/3
默认	17:03:02.422869+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.422924+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 2.1013980000463715°, 平滑后角度: 2.0097508012006733°
默认	17:03:02.423281+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.0097508012006733°, 稳定帧数: 39/3
默认	17:03:02.595155+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.595201+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.6636461832158411°, 平滑后角度: 1.8008485205407425°
默认	17:03:02.595473+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.8008485205407425°, 稳定帧数: 45/3
默认	17:03:02.446460+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.446511+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 2.1229698007472364°, 平滑后角度: 2.0725275180040184°
默认	17:03:02.618467+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.446733+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.0725275180040184°, 稳定帧数: 40/3
默认	17:03:02.618519+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.5451309235881572°, 平滑后角度: 1.7178840554223291°
默认	17:03:02.618741+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.7178840554223291°, 稳定帧数: 46/3
默认	17:03:02.740256+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.740303+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.256920293568832°, 平滑后角度: 1.6034543486374233°
默认	17:03:02.457997+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 2.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.740520+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.6034543486374233°, 稳定帧数: 47/3
默认	17:03:02.458058+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.9599532491802225°, 平滑后角度: 2.0853957919200323°
默认	17:03:02.458422+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.0853957919200323°, 稳定帧数: 41/3
默认	17:03:02.552277+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.552325+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.829068827493363°, 平滑后角度: 2.043422373805069°
默认	17:03:02.552556+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 2.043422373805069°, 稳定帧数: 42/3
默认	17:03:02.570663+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.570709+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.7827756767752037°, 平滑后角度: 1.9592331108484795°
默认	17:03:02.570951+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.9592331108484795°, 稳定帧数: 43/3
默认	17:03:02.785533+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.785630+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.2321134912173632°, 平滑后角度: 1.4933219115258551°
默认	17:03:02.786221+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.4933219115258551°, 稳定帧数: 48/3
默认	17:03:02.807353+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.807403+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.211800371312554°, 平滑后角度: 1.3819222525805497°
默认	17:03:02.807621+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.3819222525805497°, 稳定帧数: 49/3
默认	17:03:02.583936+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.583987+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.7687986660390815°, 平滑后角度: 1.8927132440470213°
默认	17:03:02.584322+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.8927132440470213°, 稳定帧数: 44/3
默认	17:03:02.823739+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.823785+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.252057954438375°, 平滑后角度: 1.2996046068250562°
默认	17:03:02.824083+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.2996046068250562°, 稳定帧数: 50/3
默认	17:03:02.595179+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.595254+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.6636461832158411°, 平滑后角度: 1.8008485205407425°
默认	17:03:02.595495+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.8008485205407425°, 稳定帧数: 45/3
默认	17:03:02.854824+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.854965+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.2172873131574566°, 平滑后角度: 1.2340358847389161°
默认	17:03:02.855205+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.2340358847389161°, 稳定帧数: 51/3
默认	17:03:02.866482+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.866529+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.208839891026431°, 平滑后角度: 1.2244198042304357°
默认	17:03:02.866741+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.2244198042304357°, 稳定帧数: 52/3
默认	17:03:02.618491+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.618541+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.5451309235881572°, 平滑后角度: 1.7178840554223291°
默认	17:03:02.618767+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.7178840554223291°, 稳定帧数: 46/3
默认	17:03:02.740278+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.740326+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.256920293568832°, 平滑后角度: 1.6034543486374233°
默认	17:03:02.740544+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.6034543486374233°, 稳定帧数: 47/3
默认	17:03:02.890732+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.890779+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.130899184626322°, 平滑后角度: 1.204176942912228°
默认	17:03:02.891035+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.204176942912228°, 稳定帧数: 53/3
默认	17:03:02.785557+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.785654+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.2321134912173632°, 平滑后角度: 1.4933219115258551°
默认	17:03:02.786247+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.4933219115258551°, 稳定帧数: 48/3
默认	17:03:02.904381+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.904428+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.9954359561425705°, 平滑后角度: 1.1609040598782312°
默认	17:03:02.904639+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1609040598782312°, 稳定帧数: 54/3
默认	17:03:02.925812+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.925859+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.0145078175899123°, 平滑后角度: 1.1133940325085385°
默认	17:03:02.926073+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1133940325085385°, 稳定帧数: 55/3
默认	17:03:02.807378+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.807425+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.211800371312554°, 平滑后角度: 1.3819222525805497°
默认	17:03:02.807645+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.3819222525805497°, 稳定帧数: 49/3
默认	17:03:02.823761+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.823808+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.252057954438375°, 平滑后角度: 1.2996046068250562°
默认	17:03:02.824106+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.2996046068250562°, 稳定帧数: 50/3
默认	17:03:02.956469+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.956515+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.9930011607560509°, 平滑后角度: 1.0685368020282573°
默认	17:03:02.957715+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.0685368020282573°, 稳定帧数: 56/3
默认	17:03:02.854846+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.854994+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.2172873131574566°, 平滑后角度: 1.2340358847389161°
默认	17:03:02.855232+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.2340358847389161°, 稳定帧数: 51/3
默认	17:03:02.974791+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.974837+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.9901424052157135°, 平滑后角度: 1.0247973048661136°
默认	17:03:02.975188+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.0247973048661136°, 稳定帧数: 57/3
默认	17:03:03.008011+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.008058+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.9685033393763975°, 平滑后角度: 0.992318135816129°
默认	17:03:03.008641+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.992318135816129°, 稳定帧数: 58/3
默认	17:03:02.866506+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.866552+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.208839891026431°, 平滑后角度: 1.2244198042304357°
默认	17:03:02.866765+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.2244198042304357°, 稳定帧数: 52/3
默认	17:03:02.890754+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.890804+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.130899184626322°, 平滑后角度: 1.204176942912228°
默认	17:03:02.891061+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.204176942912228°, 稳定帧数: 53/3
默认	17:03:03.105839+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.105885+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.0424013611520846°, 平滑后角度: 1.0017112168180318°
默认	17:03:03.106097+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.0017112168180318°, 稳定帧数: 59/3
默认	17:03:02.904405+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.904449+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.9954359561425705°, 平滑后角度: 1.1609040598782312°
默认	17:03:02.904663+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1609040598782312°, 稳定帧数: 54/3
默认	17:03:03.123801+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.123853+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.2450638877027123°, 平滑后角度: 1.0478224308405917°
默认	17:03:03.124115+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.0478224308405917°, 稳定帧数: 60/3
默认	17:03:03.143148+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.143336+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.24552428715899°, 平滑后角度: 1.0983270561211795°
默认	17:03:03.144573+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.0983270561211795°, 稳定帧数: 61/3
默认	17:03:02.925836+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.925882+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.0145078175899123°, 平滑后角度: 1.1133940325085385°
默认	17:03:02.926095+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1133940325085385°, 稳定帧数: 55/3
默认	17:03:03.158269+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.158318+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.2306353744096181°, 平滑后角度: 1.1464256499599605°
默认	17:03:03.158576+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1464256499599605°, 稳定帧数: 62/3
默认	17:03:02.956491+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.956594+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.9930011607560509°, 平滑后角度: 1.0685368020282573°
默认	17:03:02.957740+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.0685368020282573°, 稳定帧数: 56/3
默认	17:03:02.974813+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:02.974858+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.9901424052157135°, 平滑后角度: 1.0247973048661136°
默认	17:03:02.975215+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.0247973048661136°, 稳定帧数: 57/3
默认	17:03:03.268794+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.268843+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.0713061963608492°, 平滑后角度: 1.166986221356851°
默认	17:03:03.269172+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.166986221356851°, 稳定帧数: 63/3
默认	17:03:03.008033+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.008080+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.9685033393763975°, 平滑后角度: 0.992318135816129°
默认	17:03:03.008666+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.992318135816129°, 稳定帧数: 58/3
默认	17:03:03.286061+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.286108+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.8431214446507638°, 平滑后角度: 1.1271302380565866°
默认	17:03:03.286633+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1271302380565866°, 稳定帧数: 64/3
默认	17:03:03.105861+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.105907+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.0424013611520846°, 平滑后角度: 1.0017112168180318°
默认	17:03:03.106121+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.0017112168180318°, 稳定帧数: 59/3
默认	17:03:03.299583+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.9° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.299628+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.8556070149665355°, 平滑后角度: 1.0492388635093512°
默认	17:03:03.299844+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.0492388635093512°, 稳定帧数: 65/3
默认	17:03:03.314063+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.314116+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7983493357248463°, 平滑后角度: 0.9598038732225225°
默认	17:03:03.324048+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.9598038732225225°, 稳定帧数: 66/3
默认	17:03:03.123827+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.123914+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.2450638877027123°, 平滑后角度: 1.0478224308405917°
默认	17:03:03.124142+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.0478224308405917°, 稳定帧数: 60/3
默认	17:03:03.345400+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.9° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.345447+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.8698031446846782°, 平滑后角度: 0.8876374272775347°
默认	17:03:03.345758+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.8876374272775347°, 稳定帧数: 67/3
默认	17:03:03.143174+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.143382+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.24552428715899°, 平滑后角度: 1.0983270561211795°
默认	17:03:03.144597+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.0983270561211795°, 稳定帧数: 61/3
默认	17:03:03.158293+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.158341+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.2306353744096181°, 平滑后角度: 1.1464256499599605°
默认	17:03:03.158601+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1464256499599605°, 稳定帧数: 62/3
默认	17:03:03.360470+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.360516+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6672461234489019°, 平滑后角度: 0.8068254126951452°
默认	17:03:03.360728+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.8068254126951452°, 稳定帧数: 68/3
默认	17:03:03.373800+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.373847+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6572429633811736°, 平滑后角度: 0.769649716441227°
默认	17:03:03.374326+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.769649716441227°, 稳定帧数: 69/3
默认	17:03:03.268816+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.268867+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.0713061963608492°, 平滑后角度: 1.166986221356851°
默认	17:03:03.269196+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.166986221356851°, 稳定帧数: 63/3
默认	17:03:03.406608+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.406697+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7248307906249517°, 平滑后角度: 0.7434944715729104°
默认	17:03:03.406960+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.7434944715729104°, 稳定帧数: 70/3
默认	17:03:03.286084+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.286134+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.8431214446507638°, 平滑后角度: 1.1271302380565866°
默认	17:03:03.286659+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1271302380565866°, 稳定帧数: 64/3
默认	17:03:03.299606+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.9° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.299650+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.8556070149665355°, 平滑后角度: 1.0492388635093512°
默认	17:03:03.299866+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.0492388635093512°, 稳定帧数: 65/3
默认	17:03:03.441906+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.441952+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7595831481018991°, 平滑后角度: 0.7357412340483209°
默认	17:03:03.314086+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.314141+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7983493357248463°, 平滑后角度: 0.9598038732225225°
默认	17:03:03.442234+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.7357412340483209°, 稳定帧数: 71/3
默认	17:03:03.324071+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.9598038732225225°, 稳定帧数: 66/3
默认	17:03:03.345422+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.9° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.345468+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.8698031446846782°, 平滑后角度: 0.8876374272775347°
默认	17:03:03.345788+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.8876374272775347°, 稳定帧数: 67/3
默认	17:03:03.474670+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.474715+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7363526556283424°, 平滑后角度: 0.7090511362370537°
默认	17:03:03.474987+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.7090511362370537°, 稳定帧数: 72/3
默认	17:03:03.360492+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.360538+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6672461234489019°, 平滑后角度: 0.8068254126951452°
默认	17:03:03.360749+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.8068254126951452°, 稳定帧数: 68/3
默认	17:03:03.373822+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.373868+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6572429633811736°, 平滑后角度: 0.769649716441227°
默认	17:03:03.374356+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.769649716441227°, 稳定帧数: 69/3
默认	17:03:03.506944+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.506993+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6664090484198132°, 平滑后角度: 0.708883721231236°
默认	17:03:03.507275+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.708883721231236°, 稳定帧数: 73/3
默认	17:03:03.540914+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.540958+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5597759914223663°, 平滑后角度: 0.6893903268394747°
默认	17:03:03.541179+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6893903268394747°, 稳定帧数: 74/3
默认	17:03:03.406672+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.406719+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7248307906249517°, 平滑后角度: 0.7434944715729104°
默认	17:03:03.406986+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.7434944715729104°, 稳定帧数: 70/3
默认	17:03:03.581397+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.581448+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5340501923596691°, 平滑后角度: 0.6512342071864181°
默认	17:03:03.583194+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6512342071864181°, 稳定帧数: 75/3
默认	17:03:03.441930+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.441976+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7595831481018991°, 平滑后角度: 0.7357412340483209°
默认	17:03:03.442259+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.7357412340483209°, 稳定帧数: 71/3
默认	17:03:03.474693+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.474739+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7363526556283424°, 平滑后角度: 0.7090511362370537°
默认	17:03:03.475014+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.7090511362370537°, 稳定帧数: 72/3
默认	17:03:03.624145+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.624245+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.46907883335564504°, 平滑后角度: 0.5931333442371672°
默认	17:03:03.625277+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5931333442371672°, 稳定帧数: 76/3
默认	17:03:03.506968+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.507015+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6664090484198132°, 平滑后角度: 0.708883721231236°
默认	17:03:03.507298+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.708883721231236°, 稳定帧数: 73/3
默认	17:03:03.540935+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.540982+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5597759914223663°, 平滑后角度: 0.6893903268394747°
默认	17:03:03.541203+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6893903268394747°, 稳定帧数: 74/3
默认	17:03:03.642968+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.643016+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5092449052437863°, 平滑后角度: 0.5477117941602561°
默认	17:03:03.643244+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5477117941602561°, 稳定帧数: 77/3
默认	17:03:03.581420+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.581470+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5340501923596691°, 平滑后角度: 0.6512342071864181°
默认	17:03:03.583218+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6512342071864181°, 稳定帧数: 75/3
默认	17:03:03.675173+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.675224+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5432190592723752°, 平滑后角度: 0.5230737963307683°
默认	17:03:03.675384+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5230737963307683°, 稳定帧数: 78/3
默认	17:03:03.706928+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.706974+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.4514466646471596°, 平滑后角度: 0.5014079309757271°
默认	17:03:03.707398+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5014079309757271°, 稳定帧数: 79/3
默认	17:03:03.741770+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.741873+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5031947223223984°, 平滑后角度: 0.49523683696827286°
默认	17:03:03.742110+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.49523683696827286°, 稳定帧数: 80/3
默认	17:03:03.624171+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.624274+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.46907883335564504°, 平滑后角度: 0.5931333442371672°
默认	17:03:03.773830+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.625299+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5931333442371672°, 稳定帧数: 76/3
默认	17:03:03.773876+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6671464386171113°, 平滑后角度: 0.5348503580205661°
默认	17:03:03.774312+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5348503580205661°, 稳定帧数: 81/3
默认	17:03:03.807598+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.807645+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.43904021189313547°, 平滑后角度: 0.520809419350436°
默认	17:03:03.808191+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.520809419350436°, 稳定帧数: 82/3
默认	17:03:03.840956+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.841002+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.37895798654048135°, 平滑后角度: 0.4879572048040573°
默认	17:03:03.841373+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.4879572048040573°, 稳定帧数: 83/3
默认	17:03:03.642992+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.643038+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5092449052437863°, 平滑后角度: 0.5477117941602561°
默认	17:03:03.643266+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5477117941602561°, 稳定帧数: 77/3
默认	17:03:03.675196+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.675249+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5432190592723752°, 平滑后角度: 0.5230737963307683°
默认	17:03:03.675410+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5230737963307683°, 稳定帧数: 78/3
默认	17:03:03.706950+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.706996+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.4514466646471596°, 平滑后角度: 0.5014079309757271°
默认	17:03:03.707422+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5014079309757271°, 稳定帧数: 79/3
默认	17:03:03.875250+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.875297+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.45388981418481733°, 平滑后角度: 0.48844583471158876°
默认	17:03:03.875520+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.48844583471158876°, 稳定帧数: 84/3
默认	17:03:03.741847+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.741898+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5031947223223984°, 平滑后角度: 0.49523683696827286°
默认	17:03:03.742132+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.49523683696827286°, 稳定帧数: 80/3
默认	17:03:03.908675+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.908721+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5234330649729244°, 平滑后角度: 0.492493503241694°
默认	17:03:03.908936+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.492493503241694°, 稳定帧数: 85/3
默认	17:03:03.773854+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.773899+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6671464386171113°, 平滑后角度: 0.5348503580205661°
默认	17:03:03.774338+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5348503580205661°, 稳定帧数: 81/3
默认	17:03:03.807621+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.807677+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.43904021189313547°, 平滑后角度: 0.520809419350436°
默认	17:03:03.808214+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.520809419350436°, 稳定帧数: 82/3
默认	17:03:03.941343+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.941389+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5110304301537614°, 平滑后角度: 0.461270301549024°
默认	17:03:03.941615+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.461270301549024°, 稳定帧数: 86/3
默认	17:03:03.840980+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.841025+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.37895798654048135°, 平滑后角度: 0.4879572048040573°
默认	17:03:03.841403+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.4879572048040573°, 稳定帧数: 83/3
默认	17:03:03.976927+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.977027+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.4397770402182175°, 平滑后角度: 0.46141766721404043°
默认	17:03:03.980041+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.46141766721404043°, 稳定帧数: 87/3
默认	17:03:03.875272+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.875320+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.45388981418481733°, 平滑后角度: 0.48844583471158876°
默认	17:03:03.875545+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.48844583471158876°, 稳定帧数: 84/3
默认	17:03:03.908699+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.908744+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5234330649729244°, 平滑后角度: 0.492493503241694°
默认	17:03:03.908960+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.492493503241694°, 稳定帧数: 85/3
默认	17:03:03.941365+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.941411+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5110304301537614°, 平滑后角度: 0.461270301549024°
默认	17:03:03.941638+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.461270301549024°, 稳定帧数: 86/3
默认	17:03:04.007548+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.007596+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.44046883072520043°, 平滑后角度: 0.47371983605098417°
默认	17:03:04.008035+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.47371983605098417°, 稳定帧数: 88/3
默认	17:03:04.041941+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.041989+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.36159880491919594°, 平滑后角度: 0.45526163419786°
默认	17:03:04.042335+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.45526163419786°, 稳定帧数: 89/3
默认	17:03:04.073962+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.074008+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.38422492625401794°, 平滑后角度: 0.4274200064540786°
默认	17:03:04.074290+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.4274200064540786°, 稳定帧数: 90/3
默认	17:03:03.976952+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:03.977051+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.4397770402182175°, 平滑后角度: 0.46141766721404043°
默认	17:03:03.980067+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.46141766721404043°, 稳定帧数: 87/3
默认	17:03:04.107443+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.107491+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5131174564622608°, 平滑后角度: 0.4278374117157785°
默认	17:03:04.107811+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.4278374117157785°, 稳定帧数: 91/3
默认	17:03:04.007571+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.007622+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.44046883072520043°, 平滑后角度: 0.47371983605098417°
默认	17:03:04.008059+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.47371983605098417°, 稳定帧数: 88/3
默认	17:03:04.041965+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.042011+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.36159880491919594°, 平滑后角度: 0.45526163419786°
默认	17:03:04.042357+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.45526163419786°, 稳定帧数: 89/3
默认	17:03:04.073985+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.074031+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.38422492625401794°, 平滑后角度: 0.4274200064540786°
默认	17:03:04.074314+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.4274200064540786°, 稳定帧数: 90/3
默认	17:03:04.140964+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.141010+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.4849515691947098°, 平滑后角度: 0.43687231751107697°
默认	17:03:04.141228+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.43687231751107697°, 稳定帧数: 92/3
默认	17:03:04.174635+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.174688+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.42423845172760605°, 平滑后角度: 0.43362624171155806°
默认	17:03:04.174843+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.43362624171155806°, 稳定帧数: 93/3
默认	17:03:04.107467+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.107515+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5131174564622608°, 平滑后角度: 0.4278374117157785°
默认	17:03:04.107835+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.4278374117157785°, 稳定帧数: 91/3
默认	17:03:04.140986+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.141032+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.4849515691947098°, 平滑后角度: 0.43687231751107697°
默认	17:03:04.141252+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.43687231751107697°, 稳定帧数: 92/3
默认	17:03:04.174659+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.174712+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.42423845172760605°, 平滑后角度: 0.43362624171155806°
默认	17:03:04.174868+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.43362624171155806°, 稳定帧数: 93/3
默认	17:03:04.207882+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.207927+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.2979271505216515°, 平滑后角度: 0.4208919108320492°
默认	17:03:04.208228+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.4208919108320492°, 稳定帧数: 94/3
默认	17:03:04.207905+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.207951+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.2979271505216515°, 平滑后角度: 0.4208919108320492°
默认	17:03:04.208255+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.4208919108320492°, 稳定帧数: 94/3
默认	17:03:04.240634+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.240731+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.415150321045429°, 平滑后角度: 0.4270769897903314°
默认	17:03:04.241101+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.4270769897903314°, 稳定帧数: 95/3
默认	17:03:04.274080+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.274543+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.3688822824426504°, 平滑后角度: 0.39822995498640934°
默认	17:03:04.276027+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.39822995498640934°, 稳定帧数: 96/3
默认	17:03:04.307666+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.307713+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.4733859469098198°, 平滑后角度: 0.39591683052943133°
默认	17:03:04.308072+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.39591683052943133°, 稳定帧数: 97/3
默认	17:03:04.240705+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.240755+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.415150321045429°, 平滑后角度: 0.4270769897903314°
默认	17:03:04.241127+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.4270769897903314°, 稳定帧数: 95/3
默认	17:03:04.341413+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.341459+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.4838636092432103°, 平滑后角度: 0.40784186203255224°
默认	17:03:04.341804+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.40784186203255224°, 稳定帧数: 98/3
默认	17:03:04.274177+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.274631+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.3688822824426504°, 平滑后角度: 0.39822995498640934°
默认	17:03:04.276051+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.39822995498640934°, 稳定帧数: 96/3
默认	17:03:04.307688+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.307734+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.4733859469098198°, 平滑后角度: 0.39591683052943133°
默认	17:03:04.308089+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.39591683052943133°, 稳定帧数: 97/3
默认	17:03:04.341437+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.341481+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.4838636092432103°, 平滑后角度: 0.40784186203255224°
默认	17:03:04.341828+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.40784186203255224°, 稳定帧数: 98/3
默认	17:03:04.375235+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.375420+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5067258538160604°, 平滑后角度: 0.449601602691434°
默认	17:03:04.375869+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.449601602691434°, 稳定帧数: 99/3
默认	17:03:04.375382+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.375447+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5067258538160604°, 平滑后角度: 0.449601602691434°
默认	17:03:04.375893+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.449601602691434°, 稳定帧数: 99/3
默认	17:03:04.407250+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.407299+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5409764156910144°, 平滑后角度: 0.47476682162055106°
默认	17:03:04.407574+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.47476682162055106°, 稳定帧数: 100/3
默认	17:03:04.407275+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.407322+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5409764156910144°, 平滑后角度: 0.47476682162055106°
默认	17:03:04.407598+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.47476682162055106°, 稳定帧数: 100/3
默认	17:03:04.441566+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.441616+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5467034671325199°, 平滑后角度: 0.510331058558525°
默认	17:03:04.441847+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.510331058558525°, 稳定帧数: 101/3
默认	17:03:04.441591+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.441639+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5467034671325199°, 平滑后角度: 0.510331058558525°
默认	17:03:04.441869+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.510331058558525°, 稳定帧数: 101/3
默认	17:03:04.474517+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.474493+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.474618+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5493113312967034°, 平滑后角度: 0.5255161354359016°
默认	17:03:04.474596+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5493113312967034°, 平滑后角度: 0.5255161354359016°
默认	17:03:04.476578+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5255161354359016°, 稳定帧数: 102/3
默认	17:03:04.476556+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5255161354359016°, 稳定帧数: 102/3
默认	17:03:04.507332+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.507308+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.507376+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.34811169737247794°, 平滑后角度: 0.4983657530617552°
默认	17:03:04.507354+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.34811169737247794°, 平滑后角度: 0.4983657530617552°
默认	17:03:04.507606+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.4983657530617552°, 稳定帧数: 103/3
默认	17:03:04.507581+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.4983657530617552°, 稳定帧数: 103/3
默认	17:03:04.540513+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.540560+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5143774635724623°, 平滑后角度: 0.49989607501303557°
默认	17:03:04.540776+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.49989607501303557°, 稳定帧数: 104/3
默认	17:03:04.540535+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.540582+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5143774635724623°, 平滑后角度: 0.49989607501303557°
默认	17:03:04.540801+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.49989607501303557°, 稳定帧数: 104/3
默认	17:03:04.574635+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.574681+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5340173206464425°, 平滑后角度: 0.4985042560041212°
默认	17:03:04.575089+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.4985042560041212°, 稳定帧数: 105/3
默认	17:03:04.574657+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.574704+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5340173206464425°, 平滑后角度: 0.4985042560041212°
默认	17:03:04.575108+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.4985042560041212°, 稳定帧数: 105/3
默认	17:03:04.607413+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.607459+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.39378719805226337°, 平滑后角度: 0.4679210021880699°
默认	17:03:04.608016+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.4679210021880699°, 稳定帧数: 106/3
默认	17:03:04.607435+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.607481+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.39378719805226337°, 平滑后角度: 0.4679210021880699°
默认	17:03:04.608045+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.4679210021880699°, 稳定帧数: 106/3
默认	17:03:04.645854+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.645904+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.45615686815022305°, 平滑后角度: 0.44929010955877385°
默认	17:03:04.646147+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.44929010955877385°, 稳定帧数: 107/3
默认	17:03:04.645879+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.645929+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.45615686815022305°, 平滑后角度: 0.44929010955877385°
默认	17:03:04.646174+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.44929010955877385°, 稳定帧数: 107/3
默认	17:03:04.674170+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.674314+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5348943977823399°, 平滑后角度: 0.48664664964074617°
默认	17:03:04.674252+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.674339+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5348943977823399°, 平滑后角度: 0.48664664964074617°
默认	17:03:04.674463+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.48664664964074617°, 稳定帧数: 108/3
默认	17:03:04.674493+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.48664664964074617°, 稳定帧数: 108/3
默认	17:03:04.707321+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.707345+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.707367+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.587312852091797°, 平滑后角度: 0.5012337273446132°
默认	17:03:04.707449+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.587312852091797°, 平滑后角度: 0.5012337273446132°
默认	17:03:04.707746+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5012337273446132°, 稳定帧数: 109/3
默认	17:03:04.707771+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5012337273446132°, 稳定帧数: 109/3
默认	17:03:04.741301+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.741277+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.741347+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5964958911266327°, 平滑后角度: 0.5137294414406511°
默认	17:03:04.741324+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5964958911266327°, 平滑后角度: 0.5137294414406511°
默认	17:03:04.741712+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5137294414406511°, 稳定帧数: 110/3
默认	17:03:04.741688+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5137294414406511°, 稳定帧数: 110/3
默认	17:03:04.774672+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.774770+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6532088325589999°, 平滑后角度: 0.5656137683419985°
默认	17:03:04.774578+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.774744+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6532088325589999°, 平滑后角度: 0.5656137683419985°
默认	17:03:04.775386+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5656137683419985°, 稳定帧数: 111/3
默认	17:03:04.775362+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5656137683419985°, 稳定帧数: 111/3
默认	17:03:04.808036+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.808080+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7089188328869814°, 平滑后角度: 0.6161661612893502°
默认	17:03:04.808361+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6161661612893502°, 稳定帧数: 112/3
默认	17:03:04.808058+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.808104+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7089188328869814°, 平滑后角度: 0.6161661612893502°
默认	17:03:04.808383+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6161661612893502°, 稳定帧数: 112/3
默认	17:03:04.840799+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.840846+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.8163440816478498°, 平滑后角度: 0.6724560980624521°
默认	17:03:04.841125+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6724560980624521°, 稳定帧数: 113/3
默认	17:03:04.840821+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.840867+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.8163440816478498°, 平滑后角度: 0.6724560980624521°
默认	17:03:04.841147+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6724560980624521°, 稳定帧数: 113/3
默认	17:03:04.873634+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.873680+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.8371377691318167°, 平滑后角度: 0.7224210814704561°
默认	17:03:04.873984+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.7224210814704561°, 稳定帧数: 114/3
默认	17:03:04.873656+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.873750+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.8371377691318167°, 平滑后角度: 0.7224210814704561°
默认	17:03:04.874006+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.7224210814704561°, 稳定帧数: 114/3
默认	17:03:04.907763+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.9° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.907742+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.9° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.907809+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.9323844912201619°, 平滑后角度: 0.789598801489162°
默认	17:03:04.907787+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.9323844912201619°, 平滑后角度: 0.789598801489162°
默认	17:03:04.908211+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.789598801489162°, 稳定帧数: 115/3
默认	17:03:04.908185+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.789598801489162°, 稳定帧数: 115/3
默认	17:03:04.941126+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.9° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.941149+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.9° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.941226+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.9141458383372063°, 平滑后角度: 0.8417862026448031°
默认	17:03:04.941251+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.9141458383372063°, 平滑后角度: 0.8417862026448031°
默认	17:03:04.942109+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.8417862026448031°, 稳定帧数: 116/3
默认	17:03:04.942131+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.8417862026448031°, 稳定帧数: 116/3
默认	17:03:04.979955+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.980067+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.0712418319220898°, 平滑后角度: 0.9142508024518248°
默认	17:03:04.981095+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.9142508024518248°, 稳定帧数: 117/3
默认	17:03:04.979981+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:04.980093+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.0712418319220898°, 平滑后角度: 0.9142508024518248°
默认	17:03:04.981121+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.9142508024518248°, 稳定帧数: 117/3
默认	17:03:05.007430+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.007478+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.0928761520533228°, 平滑后角度: 0.9695572165329196°
默认	17:03:05.007699+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.9695572165329196°, 稳定帧数: 118/3
默认	17:03:05.007452+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.007502+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.0928761520533228°, 平滑后角度: 0.9695572165329196°
默认	17:03:05.007721+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.9695572165329196°, 稳定帧数: 118/3
默认	17:03:05.042149+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.042202+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.0880569009553145°, 平滑后角度: 1.019741042897619°
默认	17:03:05.042368+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.019741042897619°, 稳定帧数: 119/3
默认	17:03:05.042176+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.042228+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.0880569009553145°, 平滑后角度: 1.019741042897619°
默认	17:03:05.042396+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.019741042897619°, 稳定帧数: 119/3
默认	17:03:05.073999+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.074047+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.2155515203229537°, 平滑后角度: 1.0763744487181774°
默认	17:03:05.074788+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.0763744487181774°, 稳定帧数: 120/3
默认	17:03:05.074023+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.074069+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.2155515203229537°, 平滑后角度: 1.0763744487181774°
默认	17:03:05.074811+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.0763744487181774°, 稳定帧数: 120/3
默认	17:03:05.110512+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.110559+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.3015124798610145°, 平滑后角度: 1.153847777022939°
默认	17:03:05.111372+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.153847777022939°, 稳定帧数: 121/3
默认	17:03:05.110536+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.110690+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.3015124798610145°, 平滑后角度: 1.153847777022939°
默认	17:03:05.111396+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.153847777022939°, 稳定帧数: 121/3
默认	17:03:05.141166+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.141213+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.268629761443409°, 平滑后角度: 1.1933253629272027°
默认	17:03:05.141427+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1933253629272027°, 稳定帧数: 122/3
默认	17:03:05.141190+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.141237+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.268629761443409°, 平滑后角度: 1.1933253629272027°
默认	17:03:05.141450+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1933253629272027°, 稳定帧数: 122/3
默认	17:03:05.173868+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.173921+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.2381063468689217°, 平滑后角度: 1.2223714018903227°
默认	17:03:05.174150+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.2223714018903227°, 稳定帧数: 123/3
默认	17:03:05.173894+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.173945+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.2381063468689217°, 平滑后角度: 1.2223714018903227°
默认	17:03:05.174175+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.2223714018903227°, 稳定帧数: 123/3
默认	17:03:05.208165+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.208211+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.301946522332651°, 平滑后角度: 1.26514932616579°
默认	17:03:05.208355+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.26514932616579°, 稳定帧数: 124/3
默认	17:03:05.208189+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.208234+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.301946522332651°, 平滑后角度: 1.26514932616579°
默认	17:03:05.208379+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.26514932616579°, 稳定帧数: 124/3
默认	17:03:05.241099+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.241146+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.122827328143249°, 平滑后角度: 1.246604487729849°
默认	17:03:05.241122+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.241168+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.122827328143249°, 平滑后角度: 1.246604487729849°
默认	17:03:05.241367+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.246604487729849°, 稳定帧数: 125/3
默认	17:03:05.241442+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.246604487729849°, 稳定帧数: 125/3
默认	17:03:05.272768+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.272908+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.1266825387965156°, 平滑后角度: 1.2116384995169494°
默认	17:03:05.273156+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.2116384995169494°, 稳定帧数: 126/3
默认	17:03:05.272852+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.272933+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.1266825387965156°, 平滑后角度: 1.2116384995169494°
默认	17:03:05.273180+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.2116384995169494°, 稳定帧数: 126/3
默认	17:03:05.306297+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.306344+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.152679646570523°, 平滑后角度: 1.1884484765423722°
默认	17:03:05.306566+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1884484765423722°, 稳定帧数: 127/3
默认	17:03:05.306319+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.306366+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.152679646570523°, 平滑后角度: 1.1884484765423722°
默认	17:03:05.306591+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1884484765423722°, 稳定帧数: 127/3
默认	17:03:05.339143+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.339189+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.1228698115399385°, 平滑后角度: 1.1654011694765753°
默认	17:03:05.339524+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1654011694765753°, 稳定帧数: 128/3
默认	17:03:05.339165+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.339211+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.1228698115399385°, 平滑后角度: 1.1654011694765753°
默认	17:03:05.339546+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1654011694765753°, 稳定帧数: 128/3
默认	17:03:05.373976+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.374023+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.2056474563395236°, 平滑后角度: 1.1461413562779499°
默认	17:03:05.374569+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1461413562779499°, 稳定帧数: 129/3
默认	17:03:05.374001+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.374047+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.2056474563395236°, 平滑后角度: 1.1461413562779499°
默认	17:03:05.374593+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1461413562779499°, 稳定帧数: 129/3
默认	17:03:05.407041+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.407063+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.407088+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.3115355067858319°, 平滑后角度: 1.1838829920064664°
默认	17:03:05.407110+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.3115355067858319°, 平滑后角度: 1.1838829920064664°
默认	17:03:05.407306+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1838829920064664°, 稳定帧数: 130/3
默认	17:03:05.407330+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.1838829920064664°, 稳定帧数: 130/3
默认	17:03:05.440727+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.440774+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.3364096924862172°, 平滑后角度: 1.225828422744407°
默认	17:03:05.442012+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.225828422744407°, 稳定帧数: 131/3
默认	17:03:05.440751+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.440850+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.3364096924862172°, 平滑后角度: 1.225828422744407°
默认	17:03:05.442035+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.225828422744407°, 稳定帧数: 131/3
默认	17:03:05.473464+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.473510+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.3505541000580397°, 平滑后角度: 1.2654033134419103°
默认	17:03:05.473910+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.2654033134419103°, 稳定帧数: 132/3
默认	17:03:05.473486+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.473534+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.3505541000580397°, 平滑后角度: 1.2654033134419103°
默认	17:03:05.473933+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.2654033134419103°, 稳定帧数: 132/3
默认	17:03:05.514599+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.514575+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.4° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.514646+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.351066613396542°, 平滑后角度: 1.311042673813231°
默认	17:03:05.514622+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.351066613396542°, 平滑后角度: 1.311042673813231°
默认	17:03:05.515073+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.311042673813231°, 稳定帧数: 133/3
默认	17:03:05.515095+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.311042673813231°, 稳定帧数: 133/3
默认	17:03:05.539998+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.9° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.540050+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.861459863477172°, 平滑后角度: 1.2422051552407605°
默认	17:03:05.540404+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.2422051552407605°, 稳定帧数: 134/3
默认	17:03:05.540020+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.9° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.540072+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.861459863477172°, 平滑后角度: 1.2422051552407605°
默认	17:03:05.540427+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.2422051552407605°, 稳定帧数: 134/3
默认	17:03:05.573551+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.573599+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5838673753110875°, 平滑后角度: 1.0966715289458118°
默认	17:03:05.573940+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.0966715289458118°, 稳定帧数: 135/3
默认	17:03:05.573575+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.573621+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5838673753110875°, 平滑后角度: 1.0966715289458118°
默认	17:03:05.573962+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 1.0966715289458118°, 稳定帧数: 135/3
默认	17:03:05.607243+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.607219+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.607265+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.535954404047691°, 平滑后角度: 0.9365804712581063°
默认	17:03:05.607287+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.535954404047691°, 平滑后角度: 0.9365804712581063°
默认	17:03:05.607486+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.9365804712581063°, 稳定帧数: 136/3
默认	17:03:05.607508+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.9365804712581063°, 稳定帧数: 136/3
默认	17:03:05.641315+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.641362+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6941506434239799°, 平滑后角度: 0.8052997799312946°
默认	17:03:05.641513+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.8052997799312946°, 稳定帧数: 137/3
默认	17:03:05.641339+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.641385+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6941506434239799°, 平滑后角度: 0.8052997799312946°
默认	17:03:05.641535+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.8052997799312946°, 稳定帧数: 137/3
默认	17:03:05.672717+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.672761+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7649635721777699°, 平滑后角度: 0.6880791716875401°
默认	17:03:05.672905+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6880791716875401°, 稳定帧数: 138/3
默认	17:03:05.672694+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.8° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.672739+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7649635721777699°, 平滑后角度: 0.6880791716875401°
默认	17:03:05.672883+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6880791716875401°, 稳定帧数: 138/3
默认	17:03:05.708282+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.9° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.708331+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.868406423759639°, 平滑后角度: 0.6894684837440334°
默认	17:03:05.708484+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6894684837440334°, 稳定帧数: 139/3
默认	17:03:05.708307+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.9° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.708353+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.868406423759639°, 平滑后角度: 0.6894684837440334°
默认	17:03:05.708508+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6894684837440334°, 稳定帧数: 139/3
默认	17:03:05.741180+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.741226+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7292892353990124°, 平滑后角度: 0.7185528557616185°
默认	17:03:05.741394+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.7185528557616185°, 稳定帧数: 140/3
默认	17:03:05.741204+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.741252+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7292892353990124°, 平滑后角度: 0.7185528557616185°
默认	17:03:05.741424+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.7185528557616185°, 稳定帧数: 140/3
默认	17:03:05.774843+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.774893+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.12648581465599243°, 平滑后角度: 0.6366591378832787°
默认	17:03:05.775065+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6366591378832787°, 稳定帧数: 141/3
默认	17:03:05.774868+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.774919+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.12648581465599243°, 平滑后角度: 0.6366591378832787°
默认	17:03:05.775091+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6366591378832787°, 稳定帧数: 141/3
默认	17:03:05.806874+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.806921+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.11891624014511032°, 平滑后角度: 0.5216122572275048°
默认	17:03:05.807193+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5216122572275048°, 稳定帧数: 142/3
默认	17:03:05.806898+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.1° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.806942+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.11891624014511032°, 平滑后角度: 0.5216122572275048°
默认	17:03:05.807212+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5216122572275048°, 稳定帧数: 142/3
默认	17:03:05.841382+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.841429+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.15886784234240142°, 平滑后角度: 0.40039311126043103°
默认	17:03:05.841629+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.40039311126043103°, 稳定帧数: 143/3
默认	17:03:05.841406+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.841453+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.15886784234240142°, 平滑后角度: 0.40039311126043103°
默认	17:03:05.841654+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.40039311126043103°, 稳定帧数: 143/3
默认	17:03:05.874039+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.874164+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.16495847441351832°, 平滑后角度: 0.259703521391207°
默认	17:03:05.874385+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.259703521391207°, 稳定帧数: 144/3
默认	17:03:05.874109+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.874186+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.16495847441351832°, 平滑后角度: 0.259703521391207°
默认	17:03:05.874408+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.259703521391207°, 稳定帧数: 144/3
默认	17:03:05.908078+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.908127+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.24530549935654236°, 平滑后角度: 0.16290677418271296°
默认	17:03:05.908102+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.2° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.908150+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.24530549935654236°, 平滑后角度: 0.16290677418271296°
默认	17:03:05.908277+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.16290677418271296°, 稳定帧数: 145/3
默认	17:03:05.908302+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.16290677418271296°, 稳定帧数: 145/3
默认	17:03:05.944627+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.944729+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.32990337490701993°, 平滑后角度: 0.20359028623291847°
默认	17:03:05.945152+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.20359028623291847°, 稳定帧数: 146/3
默认	17:03:05.944654+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.3° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.944783+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.32990337490701993°, 平滑后角度: 0.20359028623291847°
默认	17:03:05.945177+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.20359028623291847°, 稳定帧数: 146/3
默认	17:03:05.973666+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.973690+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:05.973715+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.4890532459572287°, 平滑后角度: 0.27761768739534215°
默认	17:03:05.973737+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.4890532459572287°, 平滑后角度: 0.27761768739534215°
默认	17:03:05.974215+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.27761768739534215°, 稳定帧数: 147/3
默认	17:03:05.974242+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.27761768739534215°, 稳定帧数: 147/3
默认	17:03:06.007199+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.007244+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5332454757941633°, 平滑后角度: 0.35249321408569456°
默认	17:03:06.007175+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.007221+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5332454757941633°, 平滑后角度: 0.35249321408569456°
默认	17:03:06.007478+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.35249321408569456°, 稳定帧数: 148/3
默认	17:03:06.007463+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.35249321408569456°, 稳定帧数: 148/3
默认	17:03:06.040482+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.040529+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5444861164429291°, 平滑后角度: 0.4283987424915766°
默认	17:03:06.041913+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.4283987424915766°, 稳定帧数: 149/3
默认	17:03:06.040505+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.5° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.040553+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.5444861164429291°, 平滑后角度: 0.4283987424915766°
默认	17:03:06.041954+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.4283987424915766°, 稳定帧数: 149/3
默认	17:03:06.074317+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.074363+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6175265836018091°, 平滑后角度: 0.5028429593406301°
默认	17:03:06.074507+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5028429593406301°, 稳定帧数: 150/3
默认	17:03:06.074340+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.074385+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6175265836018091°, 平滑后角度: 0.5028429593406301°
默认	17:03:06.074531+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5028429593406301°, 稳定帧数: 150/3
默认	17:03:06.107095+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.107120+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.107142+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6858886103092023°, 平滑后角度: 0.5740400064210665°
默认	17:03:06.107165+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6858886103092023°, 平滑后角度: 0.5740400064210665°
默认	17:03:06.107489+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5740400064210665°, 稳定帧数: 151/3
默认	17:03:06.107512+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.5740400064210665°, 稳定帧数: 151/3
默认	17:03:06.139801+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.139826+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.139873+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7181609022449936°, 平滑后角度: 0.6198615376786194°
默认	17:03:06.139849+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7181609022449936°, 平滑后角度: 0.6198615376786194°
默认	17:03:06.140072+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6198615376786194°, 稳定帧数: 152/3
默认	17:03:06.140095+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6198615376786194°, 稳定帧数: 152/3
默认	17:03:06.174844+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.174901+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7211283198947563°, 平滑后角度: 0.657438106498738°
默认	17:03:06.175062+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.657438106498738°, 稳定帧数: 153/3
默认	17:03:06.174868+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.174926+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7211283198947563°, 平滑后角度: 0.657438106498738°
默认	17:03:06.175090+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.657438106498738°, 稳定帧数: 153/3
默认	17:03:06.209150+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.209199+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.610097366060172°, 平滑后角度: 0.6705603564221867°
默认	17:03:06.209719+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6705603564221867°, 稳定帧数: 154/3
默认	17:03:06.209175+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.209228+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.610097366060172°, 平滑后角度: 0.6705603564221867°
默认	17:03:06.209758+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6705603564221867°, 稳定帧数: 154/3
默认	17:03:06.241314+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.241290+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.241364+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6289211871693662°, 平滑后角度: 0.6728392771356981°
默认	17:03:06.241341+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6289211871693662°, 平滑后角度: 0.6728392771356981°
默认	17:03:06.241516+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6728392771356981°, 稳定帧数: 155/3
默认	17:03:06.241492+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6728392771356981°, 稳定帧数: 155/3
默认	17:03:06.276536+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.276941+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6821989779913289°, 平滑后角度: 0.6721013506721234°
默认	17:03:06.277121+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6721013506721234°, 稳定帧数: 156/3
默认	17:03:06.276561+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.276968+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6821989779913289°, 平滑后角度: 0.6721013506721234°
默认	17:03:06.277143+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6721013506721234°, 稳定帧数: 156/3
默认	17:03:06.307528+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.307554+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.307579+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7042521781643055°, 平滑后角度: 0.6693196058559858°
默认	17:03:06.307605+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7042521781643055°, 平滑后角度: 0.6693196058559858°
默认	17:03:06.307821+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6693196058559858°, 稳定帧数: 157/3
默认	17:03:06.307848+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6693196058559858°, 稳定帧数: 157/3
默认	17:03:06.340342+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.340390+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6145541055601632°, 平滑后角度: 0.6480047629890672°
默认	17:03:06.340548+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6480047629890672°, 稳定帧数: 158/3
默认	17:03:06.340367+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.6° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.340415+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.6145541055601632°, 平滑后角度: 0.6480047629890672°
默认	17:03:06.340574+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6480047629890672°, 稳定帧数: 158/3
默认	17:03:06.372982+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.373029+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7287305020430239°, 平滑后角度: 0.6717313901856375°
默认	17:03:06.373528+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6717313901856375°, 稳定帧数: 159/3
默认	17:03:06.373006+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 0.7° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.373054+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 0.7287305020430239°, 平滑后角度: 0.6717313901856375°
默认	17:03:06.373552+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.6717313901856375°, 稳定帧数: 159/3
默认	17:03:06.410471+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.412797+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.0158862380677713°, 平滑后角度: 0.7491244003653186°
默认	17:03:06.413077+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.7491244003653186°, 稳定帧数: 160/3
默认	17:03:06.410552+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.412884+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.0158862380677713°, 平滑后角度: 0.7491244003653186°
默认	17:03:06.413104+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.7491244003653186°, 稳定帧数: 160/3
默认	17:03:06.415081+0800	FitCount	TX focusApplication (peekAppEvent) stealKB:Y scene:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:06.415216+0800	FitCount	TX focusApplication (peekAppEvent) stealKB:Y scene:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:06.415288+0800	FitCount	Evaluating dispatch of UIEvent: 0x12a21d960; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0
默认	17:03:06.415348+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to windows: 1
默认	17:03:06.415318+0800	FitCount	Evaluating dispatch of UIEvent: 0x12a21d960; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0
默认	17:03:06.415374+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to windows: 1
默认	17:03:06.419864+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102839820>; contextId: 0x45E1D5CA
默认	17:03:06.419891+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102839820>; contextId: 0x45E1D5CA
默认	17:03:06.426310+0800	FitCount	Evaluating dispatch of UIEvent: 0x12a21d960; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0
默认	17:03:06.426378+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to windows: 1
默认	17:03:06.426444+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102839820>; contextId: 0x45E1D5CA
默认	17:03:06.426338+0800	FitCount	Evaluating dispatch of UIEvent: 0x12a21d960; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0
默认	17:03:06.426404+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to windows: 1
默认	17:03:06.426481+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102839820>; contextId: 0x45E1D5CA
默认	17:03:06.441147+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.441246+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.0155752021427629°, 平滑后角度: 0.8157996451956053°
默认	17:03:06.441457+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.8157996451956053°, 稳定帧数: 161/3
默认	17:03:06.441170+0800	FitCount	ℹ️ [SitUpCounter.swift:293] calculateTorsoAngle(from:deviceOrientation:): 最终躯干角度: 1.0° (0°=水平躺下, 90°=垂直站立)
默认	17:03:06.441271+0800	FitCount	ℹ️ [SitUpCounter.swift:323] updateAngleHistory(_:): 原始角度: 1.0155752021427629°, 平滑后角度: 0.8157996451956053°
默认	17:03:06.441482+0800	FitCount	ℹ️ [SitUpCounter.swift:343] processStateTransition(with:): 当前状态: lying_down, 角度: 0.8157996451956053°, 稳定帧数: 161/3
默认	17:03:06.443521+0800	FitCount	Evaluating dispatch of UIEvent: 0x12a21d960; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0
默认	17:03:06.443558+0800	FitCount	Evaluating dispatch of UIEvent: 0x12a21d960; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0
默认	17:03:06.457421+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to windows: 1
默认	17:03:06.457473+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102839820>; contextId: 0x45E1D5CA
默认	17:03:06.457446+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to windows: 1
默认	17:03:06.457498+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102839820>; contextId: 0x45E1D5CA
默认	17:03:06.465714+0800	FitCount	Not push traits update to screen for new style 1, <UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70)
默认	17:03:06.465873+0800	FitCount	ℹ️ [CameraManager_Extensions.swift:350] applicationWillResignActive(notification:): 应用进入后台
默认	17:03:06.466547+0800	FitCount	ℹ️ [CameraManager.swift:261] stopSession(): 停止相机会话
默认	17:03:06.465749+0800	FitCount	Not push traits update to screen for new style 1, <UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70)
默认	17:03:06.467694+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:06.465904+0800	FitCount	ℹ️ [CameraManager_Extensions.swift:350] applicationWillResignActive(notification:): 应用进入后台
默认	17:03:06.466572+0800	FitCount	ℹ️ [CameraManager.swift:261] stopSession(): 停止相机会话
默认	17:03:06.468672+0800	FitCount	Deactivation reason added: 0; deactivation reasons: 0 -> 1; animating application lifecycle event: 1
默认	17:03:06.468779+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession stopRunning]: (0x12a2b4670) (pthread:0x16de43000)
默认	17:03:06.468828+0800	FitCount	Deactivation reason added: 12; deactivation reasons: 1 -> 4097; animating application lifecycle event: 1
默认	17:03:06.467719+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:06.468697+0800	FitCount	Deactivation reason added: 0; deactivation reasons: 0 -> 1; animating application lifecycle event: 1
默认	17:03:06.468802+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession stopRunning]: (0x12a2b4670) (pthread:0x16de43000)
默认	17:03:06.468852+0800	FitCount	Deactivation reason added: 12; deactivation reasons: 1 -> 4097; animating application lifecycle event: 1
默认	17:03:06.476757+0800	FitCount	Evaluating dispatch of UIEvent: 0x12a21d960; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0
默认	17:03:06.476811+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to windows: 1
默认	17:03:06.476864+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102839820>; contextId: 0x45E1D5CA
默认	17:03:06.478509+0800	FitCount	Evaluating dispatch of UIEvent: 0x12a21d960; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0
默认	17:03:06.478585+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to windows: 1
默认	17:03:06.478652+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102839820>; contextId: 0x45E1D5CA
默认	17:03:06.476784+0800	FitCount	Evaluating dispatch of UIEvent: 0x12a21d960; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0
默认	17:03:06.476838+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to windows: 1
默认	17:03:06.476890+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102839820>; contextId: 0x45E1D5CA
默认	17:03:06.478539+0800	FitCount	Evaluating dispatch of UIEvent: 0x12a21d960; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0
默认	17:03:06.478610+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to windows: 1
默认	17:03:06.478691+0800	FitCount	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102839820>; contextId: 0x45E1D5CA
默认	17:03:06.502068+0800	FitCount	Received configuration update from daemon (initial)
默认	17:03:06.502094+0800	FitCount	Received configuration update from daemon (initial)
默认	17:03:06.570488+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _setRunning:]: (0x12a2b4670) posting AVCaptureSessionDidStopRunningNotification
默认	17:03:06.570513+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _setRunning:]: (0x12a2b4670) posting AVCaptureSessionDidStopRunningNotification
默认	17:03:06.570708+0800	FitCount	ℹ️ [CameraManager_Extensions.swift:340] sessionDidStopRunning(notification:): 收到会话停止运行通知
默认	17:03:06.570684+0800	FitCount	ℹ️ [CameraManager_Extensions.swift:340] sessionDidStopRunning(notification:): 收到会话停止运行通知
默认	17:03:07.159315+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:07.159369+0800	FitCount	Deactivation reason added: 5; deactivation reasons: 4097 -> 4129; animating application lifecycle event: 1
默认	17:03:07.160128+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:07.160180+0800	FitCount	Deactivation reason removed: 0; deactivation reasons: 4129 -> 4128; animating application lifecycle event: 1
默认	17:03:07.159288+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:07.159343+0800	FitCount	Deactivation reason added: 5; deactivation reasons: 4097 -> 4129; animating application lifecycle event: 1
默认	17:03:07.160103+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:07.160154+0800	FitCount	Deactivation reason removed: 0; deactivation reasons: 4129 -> 4128; animating application lifecycle event: 1
默认	17:03:07.194399+0800	FitCount	policyStatus:<BKSHIDEventDeliveryPolicyObserver: 0x12a1c93b0; token: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70; status: none> was:ancestor
默认	17:03:07.194933+0800	FitCount	observerPolicyDidChange: 0x12a1c93b0 -> <_UIKeyWindowSceneObserver: 0x12a2ecd20>
默认	17:03:07.195230+0800	FitCount	Scene target of keyboard event deferring environment did change: 0; scene: UIWindowScene: 0x10283b750; scene identity: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:07.194375+0800	FitCount	policyStatus:<BKSHIDEventDeliveryPolicyObserver: 0x12a1c93b0; token: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70; status: none> was:ancestor
默认	17:03:07.194910+0800	FitCount	observerPolicyDidChange: 0x12a1c93b0 -> <_UIKeyWindowSceneObserver: 0x12a2ecd20>
默认	17:03:07.195207+0800	FitCount	Scene target of keyboard event deferring environment did change: 0; scene: UIWindowScene: 0x10283b750; scene identity: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:07.222372+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:07.246867+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:07.246843+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.003319+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.003441+0800	FitCount	Deactivation reason added: 3; deactivation reasons: 4128 -> 4136; animating application lifecycle event: 1
默认	17:03:08.004400+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.004503+0800	FitCount	Deactivation reason removed: 5; deactivation reasons: 4136 -> 4104; animating application lifecycle event: 0
默认	17:03:08.003289+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.003415+0800	FitCount	Deactivation reason added: 3; deactivation reasons: 4128 -> 4136; animating application lifecycle event: 1
默认	17:03:08.004376+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.004478+0800	FitCount	Deactivation reason removed: 5; deactivation reasons: 4136 -> 4104; animating application lifecycle event: 0
默认	17:03:08.110557+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.110953+0800	FitCount	Not push traits update to screen for new style 1, <UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70)
默认	17:03:08.116503+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.116554+0800	FitCount	Deactivation reason added: 5; deactivation reasons: 4104 -> 4136; animating application lifecycle event: 1
默认	17:03:08.116817+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.116914+0800	FitCount	Deactivation reason removed: 3; deactivation reasons: 4136 -> 4128; animating application lifecycle event: 1
默认	17:03:08.118432+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.118707+0800	FitCount	Deactivation reason added: 3; deactivation reasons: 4128 -> 4136; animating application lifecycle event: 1
默认	17:03:08.121203+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.121258+0800	FitCount	Deactivation reason removed: 5; deactivation reasons: 4136 -> 4104; animating application lifecycle event: 0
默认	17:03:08.110529+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.110928+0800	FitCount	Not push traits update to screen for new style 1, <UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70)
默认	17:03:08.192786+0800	FitCount	[0x12a1d0400] invalidated because the current process cancelled the connection by calling xpc_connection_cancel()
默认	17:03:08.192888+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.192936+0800	FitCount	[0x12a1c88c0] Session canceled.
默认	17:03:08.192986+0800	FitCount	agent connection cancelled (details: Session manually canceled)
默认	17:03:08.193033+0800	FitCount	[0x12a1c88c0] Disposing of session
默认	17:03:08.214052+0800	FitCount	Deactivation reason added: 11; deactivation reasons: 4136 -> 6184; animating application lifecycle event: 0
默认	17:03:08.214961+0800	FitCount	Will add backgroundTask with taskName: <private>, expirationHandler: (null)
默认	17:03:08.215008+0800	FitCount	Creating new assertion because there is no existing background assertion.
默认	17:03:08.215056+0800	FitCount	Creating new background assertion
默认	17:03:08.215106+0800	FitCount	Created new background assertion <BKSProcessAssertion: 0x12c85e800>
默认	17:03:08.215385+0800	FitCount	Incrementing reference count for background assertion <private>
默认	17:03:08.116478+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.116529+0800	FitCount	Deactivation reason added: 5; deactivation reasons: 4104 -> 4136; animating application lifecycle event: 1
默认	17:03:08.216279+0800	FitCount	Will add backgroundTask with taskName: <private>, expirationHandler: <__NSMallocBlock__: 0x12b3c68b0>
默认	17:03:08.116794+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.216373+0800	FitCount	Reusing background assertion <BKSProcessAssertion: 0x12c85e800>
默认	17:03:08.116888+0800	FitCount	Deactivation reason removed: 3; deactivation reasons: 4136 -> 4128; animating application lifecycle event: 1
默认	17:03:08.216546+0800	FitCount	Incrementing reference count for background assertion <private>
默认	17:03:08.216690+0800	FitCount	Created background task <private>.
默认	17:03:08.217108+0800	FitCount	Ending background task with UIBackgroundTaskIdentifier: 5
默认	17:03:08.217363+0800	FitCount	Ending task with identifier 5 and description: <private>, _expireHandler: <__NSMallocBlock__: 0x12b3c68b0>
默认	17:03:08.217605+0800	FitCount	Decrementing reference count for assertion <private> (used by background task with identifier 5: <private>)
默认	17:03:08.118407+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.217715+0800	FitCount	Push traits update to screen for new style 1, <UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70)
默认	17:03:08.118683+0800	FitCount	Deactivation reason added: 3; deactivation reasons: 4128 -> 4136; animating application lifecycle event: 1
默认	17:03:08.218085+0800	FitCount	Should not send trait collection or coordinate space update, interface style 1 -> 1, <UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70)
默认	17:03:08.218826+0800	FitCount	[0x12a1ec690] [keyboardFocus] Disabling event deferring records requested: adding recreation reason: detachedContext; for reason: _UIEventDeferringManager: 0x12a1ec690: disabling keyboardFocus: context detached for window: 0x102839820; contextID: 0x45E1D5CA
默认	17:03:08.219527+0800	FitCount	Will add backgroundTask with taskName: <private>, expirationHandler: (null)
默认	17:03:08.219575+0800	FitCount	Reusing background assertion <BKSProcessAssertion: 0x12c85e800>
默认	17:03:08.219628+0800	FitCount	Incrementing reference count for background assertion <private>
默认	17:03:08.219675+0800	FitCount	Created background task <private>.
默认	17:03:08.221831+0800	FitCount	[0x12a1ec690] End local event deferring requested for token: 0x12a18c240; environments: 1; reason: UIWindowScene: 0x10283b750: end event deferring for scene invalidation
默认	17:03:08.221927+0800	FitCount	[0x12a1ec690] Removing all event deferring rules for reason: _UIEventDeferringManager: 0x12a1ec690: removing all deferring rules due to scene invalidation: 0x10283b750
默认	17:03:08.221975+0800	FitCount	Scene will invalidate: UIWindowScene: 0x10283b750; scene identity: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.222071+0800	FitCount	Stack[KeyWindow] 0x12a2eced0: Migrate scenes from SystemShellManaged -> LastOneWins
默认	17:03:08.222121+0800	FitCount	Setting default evaluation strategy for UIUserInterfaceIdiomPhone to LastOneWins
默认	17:03:08.222172+0800	FitCount	Key window needs update: 1; currentKeyWindowScene: 0x10283b750; evaluatedKeyWindowScene: 0x0; currentApplicationKeyWindow: 0x102839820; evaluatedApplicationKeyWindow: 0x0; reason: UIWindowScene: 0x10283b750: Window scene was invalidated
默认	17:03:08.222220+0800	FitCount	Window did become application key: (nil): 0x0; contextId: 0x0; scene identity: (nil)
默认	17:03:08.222269+0800	FitCount	Resetting home affordance notifier: <_UIHomeAffordanceSceneNotifier: 0x12a1ec380; observers.count: 0>; for invalidating scene: <UIWindowScene: 0x10283b750>
默认	17:03:08.222410+0800	FitCount	Enqueuing clear events of window: <UIWindow: 0x102839820>; contextId: 0x0
默认	17:03:08.222506+0800	FitCount	Performing clear events of window: <UIWindow: 0x102839820>; contextId: 0x0
默认	17:03:08.222887+0800	FitCount	Will add backgroundTask with taskName: <private>, expirationHandler: (null)
默认	17:03:08.223282+0800	FitCount	Reusing background assertion <BKSProcessAssertion: 0x12c85e800>
默认	17:03:08.223372+0800	FitCount	Incrementing reference count for background assertion <private>
默认	17:03:08.223598+0800	FitCount	Created background task <private>.
默认	17:03:08.223726+0800	FitCount	sceneOfRecord: sceneID: (null)  persistentID: (null)
默认	17:03:08.223782+0800	FitCount	[0x12ca16610] Initialized with scene: <UIWindowScene: 0x10283b750>; behavior: <_UIEventDeferringBehavior_iOS: 0x12b3de720>; availableForProcess: 1, systemShellManagesKeyboardFocus: 1
默认	17:03:08.225873+0800	FitCount	UIWindowScene: 0x10283b750: Window became key in scene: (nil): 0x0; contextId: 0x0: reason: UIWindowScene: 0x10283b750: Remove detaching window from key window history: 0x102839820
默认	17:03:08.226334+0800	FitCount	Target list changed:
默认	17:03:08.229760+0800	FitCount	ℹ️ [CameraManager.swift:85] deinit: CameraManager销毁
默认	17:03:08.229904+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession beginConfiguration]: (0x12a2b4670)
默认	17:03:08.229953+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _beginConfiguration]: (0x12a2b4670) updated beginConfigRefCount 1
默认	17:03:08.230107+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession commitConfiguration]: (0x12a2b4670)
默认	17:03:08.230268+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _commitConfiguration]: (0x12a2b4670) updated beginConfigRefCount 0
默认	17:03:08.230371+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) called. skipConfig: 0
默认	17:03:08.230510+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMinFrameDurationInternal:]: MinFrameDuration to set 1 / 30
默认	17:03:08.230556+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMaxFrameDurationInternal:]: MaxFrameDuration to set 1 / 30
默认	17:03:08.121175+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.121233+0800	FitCount	Deactivation reason removed: 5; deactivation reasons: 4136 -> 4104; animating application lifecycle event: 0
默认	17:03:08.231410+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) New fcs config(4)
默认	17:03:08.231554+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) Setting fcs config(4) FigCaptureSessionConfiguration 0x12c85f570: ID 4, AVCaptureSessionPresetHigh multiCam: 0, appAudio: 1, autoConfig: 1, mixesWithOthers: 0, runWhileMultitasking: 0, checkIfFileAlreadyExistForMFO: 1
	VC 0x12c8a05a0: <SRC:Wide back 420f/1920x1080, 30-30(max:30), Z:1.00, ICM:0, (FD E:0 B:0 S:0), HR:1, GS: 1, FaceDrivenAEAFMode:3, FaceDrivenAEAFEnabledByDefault:1, cameraMountedInLandscape: YES> -> <SINK 0x12c92de90:VideoData discards:1, preview:0, stability:0, requestedBufferAttachments.count:0>, BGRA/1920x1080, E:1, VIS:0, M:0, O:Landscape Right, DOC:0, RBC:12, CIM:0
默认	17:03:08.231776+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession dealloc]: (0x12a2b4670) (pthread 0x205e28100)
默认	17:03:08.231825+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _beginConfiguration]: (0x12a2b4670) updated beginConfigRefCount 1
默认	17:03:08.231930+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession removeInput:]: (0x12a2b4670) <AVCaptureDeviceInput: 0x12c9a3280 [后置相机]>
默认	17:03:08.231981+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _beginConfiguration]: (0x12a2b4670) updated beginConfigRefCount 2
默认	17:03:08.232256+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _commitConfiguration]: (0x12a2b4670) updated beginConfigRefCount 1
默认	17:03:08.232302+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) called. skipConfig: 1
默认	17:03:08.232348+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession removeOutput:]: (0x12a2b4670) <AVCaptureVideoDataOutput: 0x12aeed6c0>
默认	17:03:08.336754+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _beginConfiguration]: (0x12a2b4670) updated beginConfigRefCount 2
默认	17:03:08.336828+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _commitConfiguration]: (0x12a2b4670) updated beginConfigRefCount 1
默认	17:03:08.336945+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) called. skipConfig: 1
默认	17:03:08.337181+0800	FitCount	<<<< FigCaptureFrameCounter >>>> -[FigCaptureFrameCounter stop]: <0x12c9efb60[后置相机 -> <AVCaptureVideoDataOutput: 0x12aeed6c0>]> Summary: Total frames 183, fps 30.169813
默认	17:03:08.339505+0800	FitCount	ℹ️ [DeviceMotionManager.swift:89] stopMotionUpdates(): 停止设备运动数据更新
默认	17:03:08.192763+0800	FitCount	[0x12a1d0400] invalidated because the current process cancelled the connection by calling xpc_connection_cancel()
默认	17:03:08.192863+0800	FitCount	sceneOfRecord: sceneID: sceneID:com.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70  persistentID: 62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.192914+0800	FitCount	[0x12a1c88c0] Session canceled.
默认	17:03:08.192962+0800	FitCount	agent connection cancelled (details: Session manually canceled)
默认	17:03:08.193011+0800	FitCount	[0x12a1c88c0] Disposing of session
默认	17:03:08.214026+0800	FitCount	Deactivation reason added: 11; deactivation reasons: 4136 -> 6184; animating application lifecycle event: 0
默认	17:03:08.214936+0800	FitCount	Will add backgroundTask with taskName: <private>, expirationHandler: (null)
默认	17:03:08.214986+0800	FitCount	Creating new assertion because there is no existing background assertion.
默认	17:03:08.215034+0800	FitCount	Creating new background assertion
默认	17:03:08.215082+0800	FitCount	Created new background assertion <BKSProcessAssertion: 0x12c85e800>
默认	17:03:08.216255+0800	FitCount	Will add backgroundTask with taskName: <private>, expirationHandler: <__NSMallocBlock__: 0x12b3c68b0>
默认	17:03:08.216351+0800	FitCount	Reusing background assertion <BKSProcessAssertion: 0x12c85e800>
默认	17:03:08.216521+0800	FitCount	Incrementing reference count for background assertion <private>
默认	17:03:08.216666+0800	FitCount	Created background task <private>.
默认	17:03:08.217086+0800	FitCount	Ending background task with UIBackgroundTaskIdentifier: 5
默认	17:03:08.217339+0800	FitCount	Ending task with identifier 5 and description: <private>, _expireHandler: <__NSMallocBlock__: 0x12b3c68b0>
默认	17:03:08.217581+0800	FitCount	Decrementing reference count for assertion <private> (used by background task with identifier 5: <private>)
默认	17:03:08.217686+0800	FitCount	Push traits update to screen for new style 1, <UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70)
默认	17:03:08.218056+0800	FitCount	Should not send trait collection or coordinate space update, interface style 1 -> 1, <UIWindowScene: 0x10283b750> (62F58B78-9736-4008-96A1-F2F989D2CC70)
默认	17:03:08.218800+0800	FitCount	[0x12a1ec690] [keyboardFocus] Disabling event deferring records requested: adding recreation reason: detachedContext; for reason: _UIEventDeferringManager: 0x12a1ec690: disabling keyboardFocus: context detached for window: 0x102839820; contextID: 0x45E1D5CA
默认	17:03:08.219504+0800	FitCount	Will add backgroundTask with taskName: <private>, expirationHandler: (null)
默认	17:03:08.219552+0800	FitCount	Reusing background assertion <BKSProcessAssertion: 0x12c85e800>
默认	17:03:08.219603+0800	FitCount	Incrementing reference count for background assertion <private>
默认	17:03:08.219651+0800	FitCount	Created background task <private>.
默认	17:03:08.221769+0800	FitCount	Received state update for 2951 (app<com.jack.FitCount(D179D11A-42C7-4416-A55B-8D29D4BDD786)>, unknown-NotVisible
默认	17:03:08.221800+0800	FitCount	[0x12a1ec690] End local event deferring requested for token: 0x12a18c240; environments: 1; reason: UIWindowScene: 0x10283b750: end event deferring for scene invalidation
默认	17:03:08.221904+0800	FitCount	[0x12a1ec690] Removing all event deferring rules for reason: _UIEventDeferringManager: 0x12a1ec690: removing all deferring rules due to scene invalidation: 0x10283b750
默认	17:03:08.221951+0800	FitCount	Scene will invalidate: UIWindowScene: 0x10283b750; scene identity: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.jack.FitCount-62F58B78-9736-4008-96A1-F2F989D2CC70
默认	17:03:08.222047+0800	FitCount	Stack[KeyWindow] 0x12a2eced0: Migrate scenes from SystemShellManaged -> LastOneWins
默认	17:03:08.222096+0800	FitCount	Setting default evaluation strategy for UIUserInterfaceIdiomPhone to LastOneWins
默认	17:03:08.222146+0800	FitCount	Key window needs update: 1; currentKeyWindowScene: 0x10283b750; evaluatedKeyWindowScene: 0x0; currentApplicationKeyWindow: 0x102839820; evaluatedApplicationKeyWindow: 0x0; reason: UIWindowScene: 0x10283b750: Window scene was invalidated
默认	17:03:08.222198+0800	FitCount	Window did become application key: (nil): 0x0; contextId: 0x0; scene identity: (nil)
默认	17:03:08.222245+0800	FitCount	Resetting home affordance notifier: <_UIHomeAffordanceSceneNotifier: 0x12a1ec380; observers.count: 0>; for invalidating scene: <UIWindowScene: 0x10283b750>
默认	17:03:08.222387+0800	FitCount	Enqueuing clear events of window: <UIWindow: 0x102839820>; contextId: 0x0
默认	17:03:08.222481+0800	FitCount	Performing clear events of window: <UIWindow: 0x102839820>; contextId: 0x0
默认	17:03:08.222863+0800	FitCount	Will add backgroundTask with taskName: <private>, expirationHandler: (null)
默认	17:03:08.223258+0800	FitCount	Reusing background assertion <BKSProcessAssertion: 0x12c85e800>
默认	17:03:08.223332+0800	FitCount	Incrementing reference count for background assertion <private>
默认	17:03:08.223460+0800	FitCount	Created background task <private>.
默认	17:03:08.223701+0800	FitCount	sceneOfRecord: sceneID: (null)  persistentID: (null)
默认	17:03:08.223756+0800	FitCount	[0x12ca16610] Initialized with scene: <UIWindowScene: 0x10283b750>; behavior: <_UIEventDeferringBehavior_iOS: 0x12b3de720>; availableForProcess: 1, systemShellManagesKeyboardFocus: 1
默认	17:03:08.225833+0800	FitCount	UIWindowScene: 0x10283b750: Window became key in scene: (nil): 0x0; contextId: 0x0: reason: UIWindowScene: 0x10283b750: Remove detaching window from key window history: 0x102839820
默认	17:03:08.226310+0800	FitCount	Target list changed:
默认	17:03:08.229737+0800	FitCount	ℹ️ [CameraManager.swift:85] deinit: CameraManager销毁
默认	17:03:08.229881+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession beginConfiguration]: (0x12a2b4670)
默认	17:03:08.229929+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _beginConfiguration]: (0x12a2b4670) updated beginConfigRefCount 1
默认	17:03:08.230083+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession commitConfiguration]: (0x12a2b4670)
默认	17:03:08.230246+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _commitConfiguration]: (0x12a2b4670) updated beginConfigRefCount 0
默认	17:03:08.230349+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) called. skipConfig: 0
默认	17:03:08.230484+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMinFrameDurationInternal:]: MinFrameDuration to set 1 / 30
默认	17:03:08.230534+0800	FitCount	<<<< AVCaptureFigVideoDevice >>>> -[AVCaptureFigVideoDevice _setActiveVideoMaxFrameDurationInternal:]: MaxFrameDuration to set 1 / 30
默认	17:03:08.231386+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) New fcs config(4)
默认	17:03:08.231524+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) Setting fcs config(4) FigCaptureSessionConfiguration 0x12c85f570: ID 4, AVCaptureSessionPresetHigh multiCam: 0, appAudio: 1, autoConfig: 1, mixesWithOthers: 0, runWhileMultitasking: 0, checkIfFileAlreadyExistForMFO: 1
	VC 0x12c8a05a0: <SRC:Wide back 420f/1920x1080, 30-30(max:30), Z:1.00, ICM:0, (FD E:0 B:0 S:0), HR:1, GS: 1, FaceDrivenAEAFMode:3, FaceDrivenAEAFEnabledByDefault:1, cameraMountedInLandscape: YES> -> <SINK 0x12c92de90:VideoData discards:1, preview:0, stability:0, requestedBufferAttachments.count:0>, BGRA/1920x1080, E:1, VIS:0, M:0, O:Landscape Right, DOC:0, RBC:12, CIM:0
默认	17:03:08.231753+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession dealloc]: (0x12a2b4670) (pthread 0x205e28100)
默认	17:03:08.231801+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _beginConfiguration]: (0x12a2b4670) updated beginConfigRefCount 1
默认	17:03:08.231901+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession removeInput:]: (0x12a2b4670) <AVCaptureDeviceInput: 0x12c9a3280 [后置相机]>
默认	17:03:08.231959+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _beginConfiguration]: (0x12a2b4670) updated beginConfigRefCount 2
默认	17:03:08.232234+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _commitConfiguration]: (0x12a2b4670) updated beginConfigRefCount 1
默认	17:03:08.232280+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) called. skipConfig: 1
默认	17:03:08.232326+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession removeOutput:]: (0x12a2b4670) <AVCaptureVideoDataOutput: 0x12aeed6c0>
默认	17:03:08.232426+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _beginConfiguration]: (0x12a2b4670) updated beginConfigRefCount 2
默认	17:03:08.336801+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _commitConfiguration]: (0x12a2b4670) updated beginConfigRefCount 1
默认	17:03:08.336919+0800	FitCount	<<<< AVCaptureSession >>>> -[AVCaptureSession _buildAndRunGraph:]: (0x12a2b4670) called. skipConfig: 1
默认	17:03:08.337143+0800	FitCount	<<<< FigCaptureFrameCounter >>>> -[FigCaptureFrameCounter stop]: <0x12c9efb60[后置相机 -> <AVCaptureVideoDataOutput: 0x12aeed6c0>]> Summary: Total frames 183, fps 30.169813
默认	17:03:08.339483+0800	FitCount	ℹ️ [DeviceMotionManager.swift:89] stopMotionUpdates(): 停止设备运动数据更新

